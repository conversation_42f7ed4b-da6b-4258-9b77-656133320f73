<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPS Trackers - DigitalAce Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/" class="logo">
                <i class="fas fa-rocket"></i>
                DigitalAce
            </a>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-title">Main</div>
            <a href="index.html" class="menu-item">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            
            <div class="menu-title">Management</div>
            <a href="projects.html" class="menu-item">
                <i class="fas fa-project-diagram"></i>
                Projects
            </a>
            <a href="messages.html" class="menu-item">
                <i class="fas fa-comments"></i>
                Client Messages
            </a>
            <a href="support.html" class="menu-item">
                <i class="fas fa-question-circle"></i>
                Support Queries
            </a>
            <a href="users.html" class="menu-item">
                <i class="fas fa-users"></i>
                Users
            </a>
            <a href="hosting.html" class="menu-item">
                <i class="fas fa-server"></i>
                Hosting Services
            </a>
            <a href="gps-tracker.html" class="menu-item active">
                <i class="fas fa-map-marker-alt"></i>
                GPS Trackers
            </a>
            
            <div class="menu-title">Settings</div>
            <a href="settings.html" class="menu-item">
                <i class="fas fa-cog"></i>
                System Settings
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-user-shield"></i>
                Admin Accounts
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <div class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="page-title">GPS Trackers</div>
            </div>
            
            <div class="nav-right">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar">AD</div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Super Admin</div>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-bell"></i> Notifications
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Content Container -->
        <div class="content-container">
            <!-- GPS Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Devices</div>
                        <div class="stat-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                    </div>
                    <div class="stat-value">248</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 12 new this month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Active Devices</div>
                        <div class="stat-icon">
                            <i class="fas fa-satellite-dish"></i>
                        </div>
                    </div>
                    <div class="stat-value">189</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 8% from last week
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">In Stock</div>
                        <div class="stat-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                    </div>
                    <div class="stat-value">59</div>
                    <div class="stat-trend down">
                        <i class="fas fa-arrow-down"></i> 15% from last month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Battery Alerts</div>
                        <div class="stat-icon">
                            <i class="fas fa-battery-quarter"></i>
                        </div>
                    </div>
                    <div class="stat-value">12</div>
                    <div class="stat-trend">
                        <i class="fas fa-exclamation-triangle"></i> Needs attention
                    </div>
                </div>
            </div>
            
            <!-- GPS Devices Section -->
            <div class="data-section">
                <div class="section-header">
                    <div class="section-title">GPS Devices</div>
                    <div class="section-actions">
                        <select class="action-btn btn-outline" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="stock">In Stock</option>
                        </select>
                        <select class="action-btn btn-outline" id="assignmentFilter">
                            <option value="">All Assignments</option>
                            <option value="assigned">Assigned</option>
                            <option value="unassigned">Unassigned</option>
                        </select>
                        <button class="action-btn btn-outline">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="action-btn btn-primary" id="addDeviceBtn">
                            <i class="fas fa-plus"></i> Add Device
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Device</th>
                                <th>Status</th>
                                <th>Assigned To</th>
                                <th>Last Location</th>
                                <th>Battery</th>
                                <th>Last Update</th>
                                <th>Assignment Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td data-label="Device">
                                    <div class="device-info">
                                        <div class="device-icon">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div class="device-details">
                                            <h4>GPS-001</h4>
                                            <p>Model: TrackPro X1</p>
                                        </div>
                                    </div>
                                </td>
                                <td data-label="Status">
                                    <div class="device-status">
                                        <div class="status-indicator"></div>
                                        <span class="status-badge status-completed">Active</span>
                                    </div>
                                </td>
                                <td data-label="Assigned To">
                                    <div class="assignment-info">
                                        <div class="assignment-user">John Smith</div>
                                        <div class="assignment-date">Vehicle: Toyota Camry</div>
                                    </div>
                                </td>
                                <td data-label="Last Location">New York, NY</td>
                                <td data-label="Battery">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div style="width: 50px; height: 8px; background: var(--border-color); border-radius: 4px; overflow: hidden;">
                                            <div style="width: 85%; height: 100%; background: var(--success);"></div>
                                        </div>
                                        <span>85%</span>
                                    </div>
                                </td>
                                <td data-label="Last Update">2 min ago</td>
                                <td data-label="Assignment Date">Jan 15, 2024</td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="1" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="locate" data-id="1" title="Show on Map">
                                            <i class="fas fa-map"></i>
                                        </button>
                                        <button class="table-btn" data-action="assign" data-id="1" title="Assign/Reassign">
                                            <i class="fas fa-user-plus"></i>
                                        </button>
                                        <button class="table-btn" data-action="toggle" data-id="1" title="Activate/Deactivate">
                                            <i class="fas fa-power-off" style="color: var(--warning);"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="1" title="Delete Device">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td data-label="Device">
                                    <div class="device-info">
                                        <div class="device-icon" style="background: var(--secondary);">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div class="device-details">
                                            <h4>GPS-002</h4>
                                            <p>Model: TrackPro X2</p>
                                        </div>
                                    </div>
                                </td>
                                <td data-label="Status">
                                    <div class="device-status">
                                        <div class="status-indicator inactive"></div>
                                        <span class="status-badge status-urgent">Inactive</span>
                                    </div>
                                </td>
                                <td data-label="Assigned To">
                                    <div class="assignment-info">
                                        <div class="assignment-user" style="color: var(--text-secondary);">Unassigned</div>
                                        <div class="assignment-date">Available in stock</div>
                                    </div>
                                </td>
                                <td data-label="Last Location">-</td>
                                <td data-label="Battery">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div style="width: 50px; height: 8px; background: var(--border-color); border-radius: 4px; overflow: hidden;">
                                            <div style="width: 100%; height: 100%; background: var(--success);"></div>
                                        </div>
                                        <span>100%</span>
                                    </div>
                                </td>
                                <td data-label="Last Update">Never</td>
                                <td data-label="Assignment Date">-</td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="2" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="locate" data-id="2" title="Show on Map" disabled>
                                            <i class="fas fa-map"></i>
                                        </button>
                                        <button class="table-btn" data-action="assign" data-id="2" title="Assign/Reassign">
                                            <i class="fas fa-user-plus"></i>
                                        </button>
                                        <button class="table-btn" data-action="toggle" data-id="2" title="Activate/Deactivate">
                                            <i class="fas fa-power-off" style="color: var(--success);"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="2" title="Delete Device">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- View Device Modal -->
    <div class="modal-backdrop" id="viewDeviceModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-map-marker-alt"></i>
                    Device Details - <span id="deviceId">GPS-001</span>
                </h3>
                <button class="modal-close" data-modal="viewDeviceModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="device-profile">
                    <div class="device-header" style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
                        <div class="device-icon" style="width: 60px; height: 60px; font-size: 24px;">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <h4 id="deviceName">GPS Tracker GPS-001</h4>
                            <p style="color: var(--text-secondary); margin: 0;" id="deviceModel">Model: TrackPro X1</p>
                            <div class="device-status" style="margin-top: 5px;">
                                <div class="status-indicator" id="deviceStatusIndicator"></div>
                                <span id="deviceStatus" class="status-badge status-completed">Active</span>
                            </div>
                        </div>
                    </div>

                    <div class="device-info-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div class="device-info-item">
                            <strong>Serial Number:</strong> <span id="deviceSerial">TRK-001-2024</span>
                        </div>
                        <div class="device-info-item">
                            <strong>IMEI:</strong> <span id="deviceIMEI">123456789012345</span>
                        </div>
                        <div class="device-info-item">
                            <strong>Battery Level:</strong> <span id="deviceBattery">85%</span>
                        </div>
                        <div class="device-info-item">
                            <strong>Signal Strength:</strong> <span id="deviceSignal">Strong</span>
                        </div>
                        <div class="device-info-item">
                            <strong>Last Update:</strong> <span id="deviceLastUpdate">2 min ago</span>
                        </div>
                        <div class="device-info-item">
                            <strong>Firmware Version:</strong> <span id="deviceFirmware">v2.1.3</span>
                        </div>
                    </div>

                    <div class="assignment-details" style="margin-bottom: 20px;">
                        <h4>Assignment Information</h4>
                        <div style="background: var(--card-bg); border: 1px solid var(--border-color); border-radius: 8px; padding: 15px; margin-top: 10px;">
                            <div class="assignment-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div>
                                    <strong>Assigned To:</strong> <span id="assignedUser">John Smith</span>
                                </div>
                                <div>
                                    <strong>Vehicle:</strong> <span id="assignedVehicle">Toyota Camry</span>
                                </div>
                                <div>
                                    <strong>Assignment Date:</strong> <span id="assignmentDate">Jan 15, 2024</span>
                                </div>
                                <div>
                                    <strong>Purpose:</strong> <span id="assignmentPurpose">Fleet Management</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="location-info">
                        <h4>Location Information</h4>
                        <div style="background: var(--card-bg); border: 1px solid var(--border-color); border-radius: 8px; padding: 15px; margin-top: 10px;">
                            <div class="location-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div>
                                    <strong>Current Location:</strong> <span id="currentLocation">New York, NY</span>
                                </div>
                                <div>
                                    <strong>Coordinates:</strong> <span id="coordinates">40.7128, -74.0060</span>
                                </div>
                                <div>
                                    <strong>Speed:</strong> <span id="currentSpeed">0 mph</span>
                                </div>
                                <div>
                                    <strong>Direction:</strong> <span id="currentDirection">North</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" id="showOnMapBtn">
                    <i class="fas fa-map"></i>
                    Show on Map
                </button>
                <button class="modal-btn modal-btn-secondary" id="assignDeviceFromView">
                    <i class="fas fa-user-plus"></i>
                    Reassign
                </button>
                <button class="modal-btn modal-btn-primary" id="toggleDeviceFromView">
                    <i class="fas fa-power-off"></i>
                    <span id="toggleDeviceText">Deactivate</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Assign Device Modal -->
    <div class="modal-backdrop" id="assignDeviceModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    Assign GPS Device
                </h3>
                <button class="modal-close" data-modal="assignDeviceModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="assignDeviceForm">
                    <div class="form-group">
                        <label class="form-label">Select User</label>
                        <select class="form-input form-select" name="userId" required>
                            <option value="">Choose a user</option>
                            <option value="1">John Smith - Fleet Manager</option>
                            <option value="2">Jane Doe - Sales Representative</option>
                            <option value="3">Mike Johnson - Delivery Driver</option>
                            <option value="4">Sarah Wilson - Field Technician</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Vehicle Information</label>
                        <input type="text" class="form-input" name="vehicle" placeholder="e.g., Toyota Camry, License: ABC-123">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Purpose</label>
                        <select class="form-input form-select" name="purpose">
                            <option value="">Select purpose</option>
                            <option value="fleet">Fleet Management</option>
                            <option value="personal">Personal Vehicle</option>
                            <option value="delivery">Delivery Service</option>
                            <option value="field">Field Operations</option>
                            <option value="security">Security Monitoring</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Assignment Notes</label>
                        <textarea class="form-input form-textarea" name="notes" placeholder="Additional notes about this assignment..."></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="activateDevice" checked> Activate device after assignment
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="assignDeviceModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="confirmAssignDeviceBtn">
                    <i class="fas fa-user-plus"></i>
                    Assign Device
                </button>
            </div>
        </div>
    </div>

    <!-- Add Device Modal -->
    <div class="modal-backdrop" id="addDeviceModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus"></i>
                    Add New GPS Device
                </h3>
                <button class="modal-close" data-modal="addDeviceModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="addDeviceForm">
                    <div class="form-group">
                        <label class="form-label">Device ID</label>
                        <input type="text" class="form-input" name="deviceId" placeholder="e.g., GPS-003" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Device Model</label>
                        <select class="form-input form-select" name="model" required>
                            <option value="">Select model</option>
                            <option value="trackpro-x1">TrackPro X1</option>
                            <option value="trackpro-x2">TrackPro X2</option>
                            <option value="trackpro-x3">TrackPro X3</option>
                            <option value="gpsmax-pro">GPSMax Pro</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Serial Number</label>
                        <input type="text" class="form-input" name="serialNumber" placeholder="Enter serial number" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">IMEI Number</label>
                        <input type="text" class="form-input" name="imei" placeholder="15-digit IMEI number" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Firmware Version</label>
                        <input type="text" class="form-input" name="firmware" placeholder="e.g., v2.1.3">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Purchase Date</label>
                        <input type="date" class="form-input" name="purchaseDate">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Warranty Expiry</label>
                        <input type="date" class="form-input" name="warrantyExpiry">
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="addToStock" checked> Add to available stock
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="addDeviceModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="saveDeviceBtn">
                    <i class="fas fa-save"></i>
                    Add Device
                </button>
            </div>
        </div>
    </div>

    <!-- Map Modal -->
    <div class="modal-backdrop" id="mapModal">
        <div class="modal" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-map"></i>
                    Device Location - <span id="mapDeviceId">GPS-001</span>
                </h3>
                <button class="modal-close" data-modal="mapModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="map-container" style="height: 400px; background: var(--card-bg); border: 1px solid var(--border-color); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--text-secondary);">
                    <div style="text-align: center;">
                        <i class="fas fa-map" style="font-size: 48px; margin-bottom: 10px;"></i>
                        <p>Interactive map would be displayed here</p>
                        <p style="font-size: 12px;">Integration with Google Maps, OpenStreetMap, or similar service</p>
                    </div>
                </div>
                <div class="location-details" style="margin-top: 15px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div>
                        <strong>Address:</strong> <span id="mapAddress">123 Main St, New York, NY</span>
                    </div>
                    <div>
                        <strong>Coordinates:</strong> <span id="mapCoordinates">40.7128, -74.0060</span>
                    </div>
                    <div>
                        <strong>Last Update:</strong> <span id="mapLastUpdate">2 min ago</span>
                    </div>
                    <div>
                        <strong>Accuracy:</strong> <span id="mapAccuracy">±5 meters</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="mapModal">Close</button>
                <button class="modal-btn modal-btn-primary">
                    <i class="fas fa-external-link-alt"></i>
                    Open in Maps
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-backdrop" id="deleteModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Confirm Delete
                </h3>
                <button class="modal-close" data-modal="deleteModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation danger">
                    <div class="modal-icon">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                    <div class="modal-message">Are you sure you want to delete this GPS device?</div>
                    <div class="modal-description">This action cannot be undone. The device will be removed from the system and any tracking history will be lost.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="deleteModal">Cancel</button>
                <button class="modal-btn modal-btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    Delete Device
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    <script src="./assets/js/index.js"></script>
    <script src="./assets/js/gps-tracker.js"></script>
</body>
</html>
