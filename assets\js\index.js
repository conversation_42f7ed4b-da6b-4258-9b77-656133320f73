// Theme toggle functionality
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = themeToggle.querySelector('i');
        const body = document.body;
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            body.setAttribute('data-theme', savedTheme);
            themeIcon.className = savedTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
        
        // Toggle theme
        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme') || 'dark';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            body.setAttribute('data-theme', newTheme);
            themeIcon.className = newTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
            
            // Save theme preference
            localStorage.setItem('theme', newTheme);
        });
        
        // Mobile menu toggle
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        function toggleSidebar() {
            sidebar.classList.toggle('active');
            sidebarOverlay.classList.toggle('active');
            const icon = menuToggle.querySelector('i');
            if (sidebar.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        }
        
        menuToggle.addEventListener('click', toggleSidebar);
        sidebarOverlay.addEventListener('click', toggleSidebar);
        
        // User profile dropdown toggle
        const userProfile = document.getElementById('userProfile');
        const userDropdown = document.getElementById('userDropdown');
        
        userProfile.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('active');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!userProfile.contains(e.target)) {
                userDropdown.classList.remove('active');
            }
        });
        
        // Table row click functionality
        const tableRows = document.querySelectorAll('.data-table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('click', (e) => {
                if (!e.target.closest('.action-btns')) {
                    console.log('Row clicked - would show details modal');
                    // In a real app, this would open a modal with details
                }
            });
        });
        
        // Action buttons functionality
        const actionButtons = document.querySelectorAll('.action-btn, .table-btn');
        actionButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = button.getAttribute('data-action');
                const itemId = button.getAttribute('data-id');

                if (action === 'delete') {
                    // Show confirmation modal
                    if (window.modalSystem) {
                        window.modalSystem.showConfirmation(
                            'Delete Item',
                            'Are you sure you want to delete this item?',
                            () => {
                                // Handle delete action
                                if (window.notificationSystem) {
                                    window.notificationSystem.success('Deleted', 'Item has been deleted successfully.');
                                }
                                console.log(`Deleted item: ${itemId}`);
                            }
                        );
                    }
                } else {
                    console.log(`Action triggered: ${action} for item: ${itemId}`);
                    // Handle other actions (edit, view, etc.)
                }
            });
        });

        // Modal trigger buttons
        const newProjectBtn = document.getElementById('newProjectBtn');
        const newTicketBtn = document.getElementById('newTicketBtn');
        const saveProjectBtn = document.getElementById('saveProjectBtn');
        const saveTicketBtn = document.getElementById('saveTicketBtn');

        if (newProjectBtn) {
            newProjectBtn.addEventListener('click', () => {
                if (window.modalSystem) {
                    window.modalSystem.openModal('newProjectModal');
                }
            });
        }

        if (newTicketBtn) {
            newTicketBtn.addEventListener('click', () => {
                if (window.modalSystem) {
                    window.modalSystem.openModal('newTicketModal');
                }
            });
        }

        if (saveProjectBtn) {
            saveProjectBtn.addEventListener('click', () => {
                const form = document.getElementById('newProjectForm');
                if (form) {
                    form.dispatchEvent(new Event('submit'));
                }
            });
        }

        if (saveTicketBtn) {
            saveTicketBtn.addEventListener('click', () => {
                const form = document.getElementById('newTicketForm');
                if (form) {
                    form.dispatchEvent(new Event('submit'));
                }
            });
        }
        
        // Simulate data loading
        setTimeout(() => {
            console.log('Admin panel initialized');
        }, 500);