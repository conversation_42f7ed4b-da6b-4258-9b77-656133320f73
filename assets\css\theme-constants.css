/* ===================================
   DIGITALACE ADMIN THEME CONSTANTS
   ===================================
   
   This file contains all theme variables, colors, typography,
   and design constants used throughout the admin panel.
   Import this file first in all CSS files to maintain consistency.
*/

:root {
    /* ===== PRIMARY BRAND COLORS ===== */
    --primary: #7E57C2;
    --primary-dark: #5E35B1;
    --primary-light: #9575CD;
    --secondary: #26A69A;
    --secondary-dark: #00695C;
    --secondary-light: #4DB6AC;
    --accent: #FFCA28;
    --accent-dark: #FF8F00;
    --accent-light: #FFD54F;
    
    /* ===== NEUTRAL COLORS ===== */
    --dark: #263238;
    --dark-light: #37474F;
    --light: #f5f7fa;
    --light-dark: #ECEFF1;
    --gray: #78909C;
    --gray-light: #90A4AE;
    --gray-dark: #546E7A;
    --light-gray: #ECEFF1;
    --white: #FFFFFF;
    --black: #000000;
    
    /* ===== STATUS COLORS ===== */
    --success: #4CAF50;
    --success-light: #81C784;
    --success-dark: #388E3C;
    --warning: #FF9800;
    --warning-light: #FFB74D;
    --warning-dark: #F57C00;
    --error: #F44336;
    --error-light: #E57373;
    --error-dark: #D32F2F;
    --info: #2196F3;
    --info-light: #64B5F6;
    --info-dark: #1976D2;
    
    /* ===== THEME-SPECIFIC VARIABLES (DARK THEME DEFAULT) ===== */
    --bg-gradient: linear-gradient(135deg, #2c1b47 0%, #1c2331 100%);
    --bg-solid: #1c2331;
    --header-bg: rgba(38, 50, 56, 0.95);
    --card-bg: rgba(38, 50, 56, 0.8);
    --card-bg-solid: #263238;
    --text-color: #f5f7fa;
    --text-secondary: #CFD8DC;
    --text-muted: #90A4AE;
    --border-color: rgba(126, 87, 194, 0.3);
    --border-light: rgba(255, 255, 255, 0.1);
    --footer-bg: rgba(38, 50, 56, 0.95);
    --overlay-bg: rgba(0, 0, 0, 0.5);
    --hover-bg: rgba(126, 87, 194, 0.15);
    --active-bg: rgba(126, 87, 194, 0.25);
    
    /* ===== SHADOWS & EFFECTS ===== */
    --card-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.25);
    --button-shadow: 0 5px 15px rgba(126, 87, 194, 0.3);
    --button-shadow-hover: 0 8px 20px rgba(126, 87, 194, 0.4);
    --text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    --inset-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    
    /* ===== TRANSITIONS & ANIMATIONS ===== */
    --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    --transition-fast: all 0.2s ease-in-out;
    --transition-slow: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* ===== LAYOUT DIMENSIONS ===== */
    --nav-height: 70px;
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --container-max-width: 1200px;
    --content-padding: 30px;
    --section-spacing: 40px;
    
    /* ===== BORDER RADIUS ===== */
    --radius-sm: 6px;
    --radius-md: 10px;
    --radius-lg: 15px;
    --radius-xl: 20px;
    --radius-full: 50px;
    
    /* ===== TYPOGRAPHY ===== */
    --font-family-primary: 'Poppins', 'Segoe UI', sans-serif;
    --font-family-secondary: 'Inter', 'Roboto', sans-serif;
    --font-family-mono: 'Fira Code', 'Consolas', monospace;
    
    /* Font Sizes */
    --font-xs: 0.75rem;    /* 12px */
    --font-sm: 0.875rem;   /* 14px */
    --font-base: 1rem;     /* 16px */
    --font-lg: 1.125rem;   /* 18px */
    --font-xl: 1.25rem;    /* 20px */
    --font-2xl: 1.5rem;    /* 24px */
    --font-3xl: 1.875rem;  /* 30px */
    --font-4xl: 2.25rem;   /* 36px */
    --font-5xl: 3rem;      /* 48px */
    
    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    
    /* Line Heights */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.75;
    
    /* ===== SPACING SCALE ===== */
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    
    /* ===== Z-INDEX SCALE ===== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ===== LIGHT THEME OVERRIDES ===== */
[data-theme="light"] {
    --bg-gradient: linear-gradient(135deg, #f0f2f5 0%, #e4e6e9 100%);
    --bg-solid: #f0f2f5;
    --header-bg: rgba(255, 255, 255, 0.95);
    --card-bg: rgba(255, 255, 255, 0.9);
    --card-bg-solid: #ffffff;
    --text-color: #263238;
    --text-secondary: #546E7A;
    --text-muted: #78909C;
    --border-color: rgba(206, 212, 218, 0.5);
    --border-light: rgba(0, 0, 0, 0.1);
    --footer-bg: rgba(255, 255, 255, 0.95);
    --overlay-bg: rgba(0, 0, 0, 0.3);
    --hover-bg: rgba(126, 87, 194, 0.1);
    --active-bg: rgba(126, 87, 194, 0.2);
    
    /* Light theme shadows */
    --card-shadow: 0 12px 30px rgba(0, 0, 0, 0.08);
    --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.12);
    --text-shadow: none;
}

/* ===== COMPONENT-SPECIFIC CONSTANTS ===== */

/* Modal & Popup Constants */
:root {
    --modal-backdrop: rgba(0, 0, 0, 0.6);
    --modal-max-width: 600px;
    --modal-padding: var(--space-8);
    --modal-border-radius: var(--radius-lg);
}

/* Notification Constants */
:root {
    --notification-width: 350px;
    --notification-padding: var(--space-4);
    --notification-border-radius: var(--radius-md);
    --notification-duration: 5000ms;
}

/* Button Constants */
:root {
    --button-height-sm: 32px;
    --button-height-md: 40px;
    --button-height-lg: 48px;
    --button-padding-x: var(--space-4);
    --button-border-radius: var(--radius-md);
}

/* Form Constants */
:root {
    --input-height: 44px;
    --input-padding: var(--space-3) var(--space-4);
    --input-border-radius: var(--radius-md);
    --input-border-width: 2px;
}

/* Table Constants */
:root {
    --table-row-height: 60px;
    --table-header-height: 50px;
    --table-cell-padding: var(--space-4) var(--space-5);
    --table-border-radius: var(--radius-lg);
}
