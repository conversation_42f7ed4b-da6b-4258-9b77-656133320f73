/* ===================================
   NOTIFICATION SYSTEM STYLES
   ===================================
   
   Toast notifications with different types,
   auto-dismiss, and responsive design.
*/

/* ===== NOTIFICATION CONTAINER ===== */
.notification-container {
    position: fixed;
    top: var(--space-4);
    right: var(--space-4);
    z-index: var(--z-toast);
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    max-width: var(--notification-width);
    width: 100%;
    pointer-events: none;
}

/* ===== NOTIFICATION ITEM ===== */
.notification {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: var(--notification-border-radius);
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
    padding: var(--notification-padding);
    display: flex;
    align-items: flex-start;
    gap: var(--space-3);
    transform: translateX(100%);
    opacity: 0;
    transition: var(--transition);
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

/* ===== NOTIFICATION TYPES ===== */
.notification.success {
    border-left: 4px solid var(--success);
}

.notification.error {
    border-left: 4px solid var(--error);
}

.notification.warning {
    border-left: 4px solid var(--warning);
}

.notification.info {
    border-left: 4px solid var(--info);
}

/* ===== NOTIFICATION ICON ===== */
.notification-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-sm);
    color: white;
    margin-top: var(--space-1);
}

.notification.success .notification-icon {
    background: var(--success);
}

.notification.error .notification-icon {
    background: var(--error);
}

.notification.warning .notification-icon {
    background: var(--warning);
}

.notification.info .notification-icon {
    background: var(--info);
}

/* ===== NOTIFICATION CONTENT ===== */
.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: var(--font-semibold);
    color: var(--text-color);
    font-size: var(--font-sm);
    margin-bottom: var(--space-1);
    line-height: var(--leading-tight);
}

.notification-message {
    color: var(--text-secondary);
    font-size: var(--font-xs);
    line-height: var(--leading-normal);
    word-wrap: break-word;
}

/* ===== NOTIFICATION CLOSE BUTTON ===== */
.notification-close {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-xs);
}

.notification-close:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

/* ===== NOTIFICATION PROGRESS BAR ===== */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: var(--border-color);
    width: 100%;
}

.notification-progress-bar {
    height: 100%;
    background: var(--primary);
    width: 100%;
    transform-origin: left;
    animation: notificationProgress var(--notification-duration) linear forwards;
}

.notification.success .notification-progress-bar {
    background: var(--success);
}

.notification.error .notification-progress-bar {
    background: var(--error);
}

.notification.warning .notification-progress-bar {
    background: var(--warning);
}

.notification.info .notification-progress-bar {
    background: var(--info);
}

@keyframes notificationProgress {
    from {
        transform: scaleX(1);
    }
    to {
        transform: scaleX(0);
    }
}

/* ===== NOTIFICATION WITH ACTIONS ===== */
.notification-actions {
    display: flex;
    gap: var(--space-2);
    margin-top: var(--space-2);
}

.notification-action {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-sm);
    font-size: var(--font-xs);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}

.notification-action.primary {
    background: var(--primary);
    color: white;
}

.notification-action.primary:hover {
    background: var(--primary-dark);
}

.notification-action.secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.notification-action.secondary:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

/* ===== NOTIFICATION VARIANTS ===== */

/* Compact Notification */
.notification.compact {
    padding: var(--space-3);
}

.notification.compact .notification-icon {
    width: 20px;
    height: 20px;
    font-size: var(--font-xs);
}

.notification.compact .notification-title {
    font-size: var(--font-xs);
    margin-bottom: 0;
}

.notification.compact .notification-message {
    display: none;
}

/* Large Notification */
.notification.large {
    padding: var(--space-5);
    max-width: 400px;
}

.notification.large .notification-icon {
    width: 32px;
    height: 32px;
    font-size: var(--font-base);
}

.notification.large .notification-title {
    font-size: var(--font-base);
    margin-bottom: var(--space-2);
}

.notification.large .notification-message {
    font-size: var(--font-sm);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .notification-container {
        top: var(--space-2);
        right: var(--space-2);
        left: var(--space-2);
        max-width: none;
    }
    
    .notification {
        transform: translateY(-100%);
    }
    
    .notification.show {
        transform: translateY(0);
    }
    
    .notification.hide {
        transform: translateY(-100%);
    }
}

@media (max-width: 480px) {
    .notification-container {
        top: var(--space-1);
        right: var(--space-1);
        left: var(--space-1);
    }
    
    .notification {
        padding: var(--space-3);
    }
    
    .notification-actions {
        flex-direction: column;
    }
    
    .notification-action {
        justify-content: center;
    }
}

/* ===== NOTIFICATION ANIMATIONS ===== */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes slideInTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutTop {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-100%);
        opacity: 0;
    }
}

.notification.animate-in {
    animation: slideInRight 0.3s var(--bounce);
}

.notification.animate-out {
    animation: slideOutRight 0.2s ease-in;
}

@media (max-width: 768px) {
    .notification.animate-in {
        animation: slideInTop 0.3s var(--bounce);
    }
    
    .notification.animate-out {
        animation: slideOutTop 0.2s ease-in;
    }
}
