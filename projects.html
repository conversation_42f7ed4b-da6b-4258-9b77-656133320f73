<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects - DigitalAce Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                DigitalAce
            </a>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-title">Main</div>
            <a href="index.html" class="menu-item">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            
            <div class="menu-title">Management</div>
            <a href="projects.html" class="menu-item active">
                <i class="fas fa-project-diagram"></i>
                Projects
            </a>
            <a href="messages.html" class="menu-item">
                <i class="fas fa-comments"></i>
                Client Messages
            </a>
            <a href="support.html" class="menu-item">
                <i class="fas fa-question-circle"></i>
                Support Queries
            </a>
            <a href="users.html" class="menu-item">
                <i class="fas fa-users"></i>
                Users
            </a>
            <a href="hosting.html" class="menu-item">
                <i class="fas fa-server"></i>
                Hosting Services
            </a>
            <a href="gps-tracker.html" class="menu-item">
                <i class="fas fa-map-marker-alt"></i>
                GPS Trackers
            </a>
            
            <div class="menu-title">Settings</div>
            <a href="settings.html" class="menu-item">
                <i class="fas fa-cog"></i>
                System Settings
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-user-shield"></i>
                Admin Accounts
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <div class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="page-title">Projects Management</div>
            </div>
            
            <div class="nav-right">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar">AD</div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Super Admin</div>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-bell"></i> Notifications
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Content Container -->
        <div class="content-container">
            <!-- Projects Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Projects</div>
                        <div class="stat-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                    </div>
                    <div class="stat-value">42</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 12% from last month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Active Projects</div>
                        <div class="stat-icon">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="stat-value">28</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 5% from last week
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Completed</div>
                        <div class="stat-icon">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="stat-value">14</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 8% from yesterday
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Revenue</div>
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value">$124K</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 22% from last quarter
                    </div>
                </div>
            </div>
            
            <!-- Projects Section -->
            <div class="data-section">
                <div class="section-header">
                    <div class="section-title">All Projects</div>
                    <div class="section-actions">
                        <button class="action-btn btn-outline">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <button class="action-btn btn-outline">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="action-btn btn-primary" id="newProjectBtn">
                            <i class="fas fa-plus"></i> New Project
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Project Name</th>
                                <th>Client</th>
                                <th>Type</th>
                                <th>Start Date</th>
                                <th>Deadline</th>
                                <th>Progress</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>E-commerce Website</td>
                                <td>FashionHub</td>
                                <td>Web Development</td>
                                <td>15 Mar 2023</td>
                                <td>30 Jun 2023</td>
                                <td>75%</td>
                                <td><span class="status-badge status-pending">In Progress</span></td>
                                <td>
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="edit" data-id="1">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-btn" data-action="view" data-id="1">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="1">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Mobile App UI/UX</td>
                                <td>TechSolutions</td>
                                <td>App Design</td>
                                <td>01 Apr 2023</td>
                                <td>15 Jul 2023</td>
                                <td>45%</td>
                                <td><span class="status-badge status-pending">In Progress</span></td>
                                <td>
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="edit" data-id="2">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-btn" data-action="view" data-id="2">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="2">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Brand Identity</td>
                                <td>StartUp Inc</td>
                                <td>Graphic Design</td>
                                <td>10 May 2023</td>
                                <td>25 May 2023</td>
                                <td>100%</td>
                                <td><span class="status-badge status-completed">Completed</span></td>
                                <td>
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="edit" data-id="3">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-btn" data-action="view" data-id="3">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="3">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- New Project Modal -->
    <div class="modal-backdrop" id="newProjectModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus"></i>
                    Create New Project
                </h3>
                <button class="modal-close" data-modal="newProjectModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="newProjectForm">
                    <div class="form-group">
                        <label class="form-label">Project Name</label>
                        <input type="text" class="form-input" name="projectName" placeholder="Enter project name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Client</label>
                        <input type="text" class="form-input" name="client" placeholder="Enter client name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Project Type</label>
                        <select class="form-input form-select" name="projectType" required>
                            <option value="">Select project type</option>
                            <option value="web-development">Web Development</option>
                            <option value="app-design">App Design</option>
                            <option value="graphic-design">Graphic Design</option>
                            <option value="marketing">Marketing</option>
                            <option value="video-editing">Video Editing</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Start Date</label>
                        <input type="date" class="form-input" name="startDate" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Deadline</label>
                        <input type="date" class="form-input" name="deadline" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea class="form-input form-textarea" name="description" placeholder="Enter project description"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="newProjectModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="saveProjectBtn">
                    <i class="fas fa-save"></i>
                    Create Project
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-backdrop" id="deleteModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Confirm Delete
                </h3>
                <button class="modal-close" data-modal="deleteModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation danger">
                    <div class="modal-icon">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                    <div class="modal-message">Are you sure you want to delete this project?</div>
                    <div class="modal-description">This action cannot be undone. All data associated with this project will be permanently removed.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="deleteModal">Cancel</button>
                <button class="modal-btn modal-btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    Delete
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    <script src="./assets/js/index.js"></script>
</body>
</html>
