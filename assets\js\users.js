/* ===================================
   USERS MANAGEMENT PAGE FUNCTIONALITY
   ===================================
   
   Handles user management, CRUD operations,
   banning, role management, and user interactions.
*/

class UsersManager {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeFilters();
    }

    bindEvents() {
        // User action buttons
        document.addEventListener('click', (e) => {
            const action = e.target.closest('[data-action]')?.getAttribute('data-action');
            const userId = e.target.closest('[data-id]')?.getAttribute('data-id');

            if (action && userId) {
                this.handleUserAction(action, userId);
            }
        });

        // Add user button
        const addUserBtn = document.getElementById('addUserBtn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => {
                window.modalSystem.openModal('addUserModal');
            });
        }

        // Filters
        const roleFilter = document.getElementById('roleFilter');
        const statusFilter = document.getElementById('statusFilter');

        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.filterUsers('role', e.target.value);
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterUsers('status', e.target.value);
            });
        }

        // Modal action buttons
        this.bindModalActions();
    }

    bindModalActions() {
        // Create user
        const createUserBtn = document.getElementById('createUserBtn');
        if (createUserBtn) {
            createUserBtn.addEventListener('click', () => {
                this.createUser();
            });
        }

        // Save user changes
        const saveUserBtn = document.getElementById('saveUserBtn');
        if (saveUserBtn) {
            saveUserBtn.addEventListener('click', () => {
                this.saveUserChanges();
            });
        }

        // Ban user confirmation
        const confirmBanBtn = document.getElementById('confirmBanBtn');
        if (confirmBanBtn) {
            confirmBanBtn.addEventListener('click', () => {
                this.banUser();
            });
        }

        // Profile action buttons
        const editUserFromProfile = document.getElementById('editUserFromProfile');
        const banUserFromProfile = document.getElementById('banUserFromProfile');
        const deleteUserFromProfile = document.getElementById('deleteUserFromProfile');

        if (editUserFromProfile) {
            editUserFromProfile.addEventListener('click', () => {
                this.editUserFromProfile();
            });
        }

        if (banUserFromProfile) {
            banUserFromProfile.addEventListener('click', () => {
                this.openBanModal();
            });
        }

        if (deleteUserFromProfile) {
            deleteUserFromProfile.addEventListener('click', () => {
                this.deleteUserFromProfile();
            });
        }
    }

    handleUserAction(action, userId) {
        this.currentUser = userId;

        switch (action) {
            case 'view':
                this.viewUser(userId);
                break;
            case 'edit':
                this.editUser(userId);
                break;
            case 'ban':
                this.openBanModal(userId);
                break;
            case 'delete':
                this.deleteUser(userId);
                break;
        }
    }

    viewUser(userId) {
        const userData = this.getUserData(userId);
        
        // Populate modal with user data
        document.getElementById('userName').textContent = userData.name;
        document.getElementById('userFullName').textContent = userData.name;
        document.getElementById('userId').textContent = `#USR-${userId.padStart(3, '0')}`;
        document.getElementById('userEmail').textContent = userData.email;
        document.getElementById('userPhone').textContent = userData.phone;
        document.getElementById('userLocation').textContent = userData.location;
        document.getElementById('userJoinDate').textContent = userData.joined;
        document.getElementById('userLastLogin').textContent = userData.lastLogin;
        document.getElementById('userProjects').textContent = userData.projects;
        document.getElementById('userMessages').textContent = userData.messages;
        document.getElementById('userTickets').textContent = userData.tickets;

        // Update avatar
        const avatar = document.getElementById('userAvatarLarge');
        avatar.textContent = this.getInitials(userData.name);
        avatar.style.background = userData.avatarColor || 'var(--primary)';

        // Update role badge
        const roleElement = document.getElementById('userRole');
        roleElement.className = `status-badge status-${this.getRoleClass(userData.role)}`;
        roleElement.textContent = userData.role;

        // Update status badge
        const statusElement = document.getElementById('userStatus');
        statusElement.className = `status-badge status-${this.getStatusClass(userData.status)}`;
        statusElement.textContent = userData.status;

        // Update permissions
        document.getElementById('userCanCreate').textContent = userData.permissions.canCreate ? 'Yes' : 'No';
        document.getElementById('userCanManage').textContent = userData.permissions.canManage ? 'Yes' : 'No';
        document.getElementById('userCanReports').textContent = userData.permissions.canReports ? 'Yes' : 'No';

        window.modalSystem.openModal('viewUserModal');
    }

    editUser(userId) {
        const userData = this.getUserData(userId);
        const form = document.getElementById('editUserForm');
        
        // Populate form with user data
        form.fullName.value = userData.name;
        form.email.value = userData.email;
        form.phone.value = userData.phone;
        form.location.value = userData.location;
        form.role.value = userData.role.toLowerCase();
        form.status.value = userData.status.toLowerCase();
        
        // Set permissions
        form.canCreate.checked = userData.permissions.canCreate;
        form.canManage.checked = userData.permissions.canManage;
        form.canReports.checked = userData.permissions.canReports;

        window.modalSystem.openModal('editUserModal');
    }

    editUserFromProfile() {
        window.modalSystem.closeModal('viewUserModal');
        this.editUser(this.currentUser);
    }

    deleteUserFromProfile() {
        window.modalSystem.closeModal('viewUserModal');
        this.deleteUser(this.currentUser);
    }

    createUser() {
        const form = document.getElementById('addUserForm');
        const formData = new FormData(form);
        const userData = Object.fromEntries(formData.entries());

        // Validate required fields
        if (!userData.fullName || !userData.email || !userData.password || !userData.role) {
            window.notificationSystem.error('Validation Error', 'Please fill in all required fields');
            return;
        }

        // Simulate API call
        setTimeout(() => {
            window.modalSystem.closeModal('addUserModal');
            window.notificationSystem.success('User Created', 'New user has been created successfully');
            
            if (userData.sendWelcomeEmail) {
                window.notificationSystem.info('Welcome Email', 'Welcome email has been sent to the user');
            }
            
            // Reset form
            form.reset();
            
            console.log('New user created:', userData);
        }, 500);
    }

    saveUserChanges() {
        const form = document.getElementById('editUserForm');
        const formData = new FormData(form);
        const userData = Object.fromEntries(formData.entries());

        // Validate required fields
        if (!userData.fullName || !userData.email || !userData.role || !userData.status) {
            window.notificationSystem.error('Validation Error', 'Please fill in all required fields');
            return;
        }

        // Update UI immediately
        this.updateUserInTable(this.currentUser, userData);

        // Simulate API call
        setTimeout(() => {
            window.modalSystem.closeModal('editUserModal');
            window.notificationSystem.success('User Updated', 'User information has been updated successfully');
            
            console.log('User updated:', userData);
        }, 500);
    }

    openBanModal(userId = null) {
        const id = userId || this.currentUser;
        this.currentUser = id;
        
        // Clear previous reason
        document.getElementById('banReason').value = '';
        
        window.modalSystem.closeModal('viewUserModal');
        window.modalSystem.openModal('banUserModal');
    }

    banUser() {
        const reason = document.getElementById('banReason').value;
        
        // Update user status in table
        this.updateUserStatus(this.currentUser, 'banned');

        window.modalSystem.closeModal('banUserModal');
        window.notificationSystem.warning('User Banned', 'User has been banned successfully');
        
        if (reason) {
            console.log(`User ${this.currentUser} banned. Reason: ${reason}`);
        } else {
            console.log(`User ${this.currentUser} banned. No reason provided.`);
        }
    }

    deleteUser(userId) {
        window.modalSystem.showConfirmation(
            'Delete User',
            'Are you sure you want to delete this user?',
            () => {
                // Remove from UI
                const row = document.querySelector(`[data-id="${userId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }
                
                window.notificationSystem.success('User Deleted', 'User has been deleted successfully');
                console.log(`User ${userId} deleted`);
            }
        );
    }

    updateUserInTable(userId, userData) {
        const row = document.querySelector(`[data-id="${userId}"]`).closest('tr');
        if (!row) return;

        // Update name
        const nameCell = row.querySelector('[data-label="User"] div div');
        if (nameCell) {
            nameCell.textContent = userData.fullName;
        }

        // Update email
        const emailCell = row.querySelector('[data-label="Email"]');
        if (emailCell) {
            emailCell.textContent = userData.email;
        }

        // Update role
        const roleCell = row.querySelector('[data-label="Role"] .status-badge');
        if (roleCell) {
            roleCell.className = `status-badge status-${this.getRoleClass(userData.role)}`;
            roleCell.textContent = userData.role.charAt(0).toUpperCase() + userData.role.slice(1);
        }

        // Update status
        const statusCell = row.querySelector('[data-label="Status"] .status-badge');
        if (statusCell) {
            statusCell.className = `status-badge status-${this.getStatusClass(userData.status)}`;
            statusCell.textContent = userData.status.charAt(0).toUpperCase() + userData.status.slice(1);
        }
    }

    updateUserStatus(userId, status) {
        const row = document.querySelector(`[data-id="${userId}"]`).closest('tr');
        const statusCell = row?.querySelector('[data-label="Status"] .status-badge');
        
        if (statusCell) {
            statusCell.className = `status-badge status-${this.getStatusClass(status)}`;
            statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        }
    }

    filterUsers(filterType, value) {
        const rows = document.querySelectorAll('.data-table tbody tr');
        
        rows.forEach(row => {
            if (!value) {
                row.style.display = '';
                return;
            }

            let cellSelector;
            if (filterType === 'role') {
                cellSelector = '[data-label="Role"] .status-badge';
            } else if (filterType === 'status') {
                cellSelector = '[data-label="Status"] .status-badge';
            }

            const cell = row.querySelector(cellSelector);
            const cellValue = cell?.textContent.toLowerCase();
            
            row.style.display = cellValue === value ? '' : 'none';
        });
    }

    getRoleClass(role) {
        const roleMap = {
            'admin': 'completed',
            'moderator': 'warning',
            'user': 'pending',
            'client': 'info'
        };
        return roleMap[role.toLowerCase()] || 'pending';
    }

    getStatusClass(status) {
        const statusMap = {
            'active': 'completed',
            'inactive': 'pending',
            'banned': 'urgent',
            'pending': 'warning'
        };
        return statusMap[status.toLowerCase()] || 'pending';
    }

    getInitials(name) {
        return name.split(' ').map(n => n[0]).join('').toUpperCase();
    }

    initializeFilters() {
        // Initialize any default filters or sorting
    }

    getUserData(userId) {
        // Mock data - in real app, fetch from API
        const mockData = {
            '1': {
                name: 'John Doe',
                email: '<EMAIL>',
                phone: '+****************',
                location: 'New York, USA',
                role: 'Admin',
                status: 'Active',
                joined: 'Jan 15, 2023',
                lastLogin: '2 hours ago',
                projects: '12',
                messages: '45',
                tickets: '3',
                avatarColor: 'var(--primary)',
                permissions: {
                    canCreate: true,
                    canManage: true,
                    canReports: true
                }
            },
            '2': {
                name: 'Jane Smith',
                email: '<EMAIL>',
                phone: '+****************',
                location: 'Los Angeles, USA',
                role: 'User',
                status: 'Active',
                joined: 'Feb 20, 2023',
                lastLogin: '1 day ago',
                projects: '8',
                messages: '23',
                tickets: '1',
                avatarColor: 'var(--secondary)',
                permissions: {
                    canCreate: true,
                    canManage: false,
                    canReports: true
                }
            }
        };

        return mockData[userId] || mockData['1'];
    }
}

// Initialize users manager
document.addEventListener('DOMContentLoaded', () => {
    window.usersManager = new UsersManager();
});
