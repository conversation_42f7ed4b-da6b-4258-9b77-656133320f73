/* ===================================
   MODAL & POPUP SYSTEM STYLES
   ===================================
   
   Reusable modal system with responsive design,
   theme support, and consistent styling.
*/

/* ===== MODAL BACKDROP ===== */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--modal-backdrop);
    backdrop-filter: blur(8px);
    z-index: var(--z-modal-backdrop);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    padding: var(--space-4);
}

.modal-backdrop.active {
    opacity: 1;
    visibility: visible;
}

/* ===== MODAL CONTAINER ===== */
.modal {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: var(--modal-border-radius);
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow-hover);
    max-width: var(--modal-max-width);
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.8) translateY(50px);
    transition: var(--transition);
    position: relative;
}

.modal-backdrop.active .modal {
    transform: scale(1) translateY(0);
}

/* ===== MODAL HEADER ===== */
.modal-header {
    padding: var(--space-6) var(--modal-padding) var(--space-4);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--header-bg);
    backdrop-filter: blur(10px);
}

.modal-title {
    font-size: var(--font-xl);
    font-weight: var(--font-semibold);
    color: var(--text-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.modal-title i {
    color: var(--accent);
    font-size: var(--font-lg);
}

.modal-close {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: var(--font-xl);
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.modal-close:hover {
    background: var(--hover-bg);
    color: var(--error);
    transform: rotate(90deg);
}

/* ===== MODAL BODY ===== */
.modal-body {
    padding: var(--modal-padding);
    max-height: 60vh;
    overflow-y: auto;
    color: var(--text-color);
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: var(--border-color);
    border-radius: var(--radius-sm);
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: var(--radius-sm);
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* ===== MODAL FOOTER ===== */
.modal-footer {
    padding: var(--space-4) var(--modal-padding) var(--space-6);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
    background: var(--header-bg);
    backdrop-filter: blur(10px);
}

/* ===== MODAL BUTTONS ===== */
.modal-btn {
    padding: var(--space-3) var(--space-5);
    border-radius: var(--button-border-radius);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-sm);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    border: none;
    min-width: 100px;
    justify-content: center;
}

.modal-btn-primary {
    background: var(--primary);
    color: white;
    box-shadow: var(--button-shadow);
}

.modal-btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--button-shadow-hover);
}

.modal-btn-secondary {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-color);
}

.modal-btn-secondary:hover {
    background: var(--hover-bg);
    border-color: var(--primary);
    color: var(--primary);
}

.modal-btn-danger {
    background: var(--error);
    color: white;
    box-shadow: 0 5px 15px rgba(244, 67, 54, 0.3);
}

.modal-btn-danger:hover {
    background: var(--error-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(244, 67, 54, 0.4);
}

/* ===== MODAL CONTENT TYPES ===== */

/* Form Modal */
.modal-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form-label {
    font-weight: var(--font-medium);
    color: var(--text-color);
    font-size: var(--font-sm);
}

.form-input {
    padding: var(--input-padding);
    border: var(--input-border-width) solid var(--border-color);
    border-radius: var(--input-border-radius);
    background: var(--card-bg);
    color: var(--text-color);
    font-size: var(--font-base);
    transition: var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(126, 87, 194, 0.1);
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

/* Confirmation Modal */
.modal-confirmation {
    text-align: center;
}

.modal-confirmation .modal-icon {
    font-size: var(--font-4xl);
    margin-bottom: var(--space-4);
    color: var(--warning);
}

.modal-confirmation.danger .modal-icon {
    color: var(--error);
}

.modal-confirmation.success .modal-icon {
    color: var(--success);
}

.modal-confirmation .modal-message {
    font-size: var(--font-lg);
    margin-bottom: var(--space-2);
    color: var(--text-color);
}

.modal-confirmation .modal-description {
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .modal {
        max-width: 95%;
        margin: var(--space-4);
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: var(--space-4);
        padding-right: var(--space-4);
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-btn {
        width: 100%;
    }
    
    .modal-title {
        font-size: var(--font-lg);
    }
}

@media (max-width: 480px) {
    .modal-backdrop {
        padding: var(--space-2);
    }
    
    .modal {
        max-width: 100%;
    }
    
    .modal-body {
        max-height: 50vh;
    }
}

/* ===== MODAL ANIMATIONS ===== */
@keyframes modalSlideIn {
    from {
        transform: scale(0.8) translateY(50px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes modalSlideOut {
    from {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
    to {
        transform: scale(0.8) translateY(50px);
        opacity: 0;
    }
}

.modal.animate-in {
    animation: modalSlideIn 0.3s var(--bounce);
}

.modal.animate-out {
    animation: modalSlideOut 0.2s ease-in;
}

/* ===== SETTINGS MODAL STYLES ===== */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-4);
}

.settings-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    transition: var(--transition-fast);
}

.settings-card:hover {
    border-color: var(--primary);
    box-shadow: var(--card-shadow);
}

.settings-card-title {
    font-weight: var(--font-semibold);
    color: var(--text-color);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.settings-card-title i {
    color: var(--primary);
}

.settings-card-description {
    color: var(--text-secondary);
    font-size: var(--font-sm);
    margin-bottom: var(--space-3);
}

.settings-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-2) 0;
}

.toggle-switch {
    position: relative;
    width: 50px;
    height: 24px;
    background: var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.toggle-switch.active {
    background: var(--primary);
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.toggle-switch.active::after {
    transform: translateX(26px);
}

/* ===== TICKET DETAILS STYLES ===== */
.ticket-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.ticket-info-item {
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--border-color);
}

.ticket-description,
.ticket-history {
    margin-top: var(--space-4);
}

.ticket-text {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-top: var(--space-2);
    line-height: var(--leading-relaxed);
    color: var(--text-color);
}

.history-timeline {
    margin-top: var(--space-2);
}

.history-item {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-2) 0;
    border-left: 3px solid var(--primary);
    padding-left: var(--space-3);
    margin-left: var(--space-2);
    position: relative;
}

.history-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
    background: var(--primary);
    border-radius: 50%;
}

.history-time {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    min-width: 100px;
}

.history-action {
    color: var(--text-color);
}

/* ===== USER MANAGEMENT STYLES ===== */
.user-avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-bold);
    color: white;
    font-size: var(--font-lg);
}

.user-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-4);
    margin-top: var(--space-4);
}

.user-detail-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-4);
}

.user-detail-title {
    font-weight: var(--font-semibold);
    color: var(--text-color);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.user-detail-content {
    color: var(--text-secondary);
    font-size: var(--font-sm);
}

.user-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-2);
    margin-top: var(--space-4);
}

.user-action-btn {
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-1);
}

.user-action-btn.ban {
    background: var(--warning);
    color: white;
}

.user-action-btn.ban:hover {
    background: var(--warning-dark);
}

.user-action-btn.delete {
    background: var(--error);
    color: white;
}

.user-action-btn.delete:hover {
    background: var(--error-dark);
}

.user-action-btn.edit {
    background: var(--primary);
    color: white;
}

.user-action-btn.edit:hover {
    background: var(--primary-dark);
}

/* ===== HOSTING FEATURES STYLES ===== */
.features-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    margin-top: var(--space-2);
}

.feature-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-2) var(--space-3);
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.remove-feature-btn {
    background: var(--error);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--font-xs);
    transition: var(--transition-fast);
}

.remove-feature-btn:hover {
    background: var(--error-dark);
    transform: scale(1.1);
}

.quick-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.quick-feature-btn {
    padding: var(--space-2) var(--space-3);
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--font-sm);
    transition: var(--transition-fast);
}

.quick-feature-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* ===== GPS TRACKER STYLES ===== */
.device-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success);
    animation: pulse 2s infinite;
}

.status-indicator.inactive {
    background: var(--error);
    animation: none;
}

.status-indicator.maintenance {
    background: var(--warning);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

.device-info {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.device-icon {
    width: 32px;
    height: 32px;
    background: var(--primary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-sm);
}

.device-details h4 {
    margin: 0;
    font-size: var(--font-sm);
    font-weight: var(--font-semibold);
}

.device-details p {
    margin: 0;
    font-size: var(--font-xs);
    color: var(--text-secondary);
}

.assignment-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.assignment-user {
    font-weight: var(--font-medium);
    color: var(--text-color);
}

.assignment-date {
    font-size: var(--font-xs);
    color: var(--text-secondary);
}
