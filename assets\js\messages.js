/* ===================================
   CLIENT MESSAGES PAGE FUNCTIONALITY
   ===================================
   
   Handles message management, status updates,
   bulk actions, and message interactions.
*/

class MessagesManager {
    constructor() {
        this.selectedMessages = new Set();
        this.currentMessage = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeFilters();
    }

    bindEvents() {
        // Message action buttons
        document.addEventListener('click', (e) => {
            const action = e.target.closest('[data-action]')?.getAttribute('data-action');
            const messageId = e.target.closest('[data-id]')?.getAttribute('data-id');

            if (action && messageId) {
                this.handleMessageAction(action, messageId, e.target);
            }
        });

        // Bulk selection
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }

        // Individual checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('bulk-checkbox') && e.target.id !== 'selectAll') {
                this.toggleMessageSelection(e.target.getAttribute('data-id'), e.target.checked);
            }
        });

        // Bulk actions button
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        if (bulkActionsBtn) {
            bulkActionsBtn.addEventListener('click', () => {
                this.showBulkActions();
            });
        }

        // Status filter
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterByStatus(e.target.value);
            });
        }

        // Modal action buttons
        this.bindModalActions();
    }

    bindModalActions() {
        // Accept message
        const acceptBtn = document.getElementById('acceptMessageBtn');
        if (acceptBtn) {
            acceptBtn.addEventListener('click', () => {
                this.updateMessageStatus(this.currentMessage, 'accepted');
                window.modalSystem.closeModal('viewMessageModal');
            });
        }

        // Decline message
        const declineBtn = document.getElementById('declineMessageBtn');
        if (declineBtn) {
            declineBtn.addEventListener('click', () => {
                this.updateMessageStatus(this.currentMessage, 'declined');
                window.modalSystem.closeModal('viewMessageModal');
            });
        }

        // Reply button
        const replyBtn = document.getElementById('replyMessageBtn');
        if (replyBtn) {
            replyBtn.addEventListener('click', () => {
                this.openReplyModal();
            });
        }

        // Send reply
        const sendReplyBtn = document.getElementById('sendReplyBtn');
        if (sendReplyBtn) {
            sendReplyBtn.addEventListener('click', () => {
                this.sendReply();
            });
        }

        // Bulk action buttons
        const bulkAcceptBtn = document.getElementById('bulkAcceptBtn');
        const bulkDeclineBtn = document.getElementById('bulkDeclineBtn');
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');

        if (bulkAcceptBtn) {
            bulkAcceptBtn.addEventListener('click', () => {
                this.bulkUpdateStatus('accepted');
            });
        }

        if (bulkDeclineBtn) {
            bulkDeclineBtn.addEventListener('click', () => {
                this.bulkUpdateStatus('declined');
            });
        }

        if (bulkDeleteBtn) {
            bulkDeleteBtn.addEventListener('click', () => {
                this.bulkDelete();
            });
        }
    }

    handleMessageAction(action, messageId, element) {
        this.currentMessage = messageId;

        switch (action) {
            case 'view':
                this.viewMessage(messageId);
                break;
            case 'accept':
                this.updateMessageStatus(messageId, 'accepted');
                break;
            case 'decline':
                this.updateMessageStatus(messageId, 'declined');
                break;
            case 'reply':
                this.openReplyModal(messageId);
                break;
            case 'delete':
                this.deleteMessage(messageId);
                break;
        }
    }

    viewMessage(messageId) {
        // In a real app, fetch message details from API
        const messageData = this.getMessageData(messageId);
        
        // Populate modal with message data
        document.getElementById('messageFrom').textContent = messageData.name;
        document.getElementById('messageEmail').textContent = messageData.email;
        document.getElementById('messageDate').textContent = messageData.date;
        document.getElementById('messageSubject').textContent = messageData.subject;
        document.getElementById('messageContent').textContent = messageData.content;

        window.modalSystem.openModal('viewMessageModal');
    }

    updateMessageStatus(messageId, status) {
        // Update UI immediately
        const row = document.querySelector(`[data-id="${messageId}"]`).closest('tr');
        const statusCell = row.querySelector('[data-label="Status"] .status-badge');
        
        if (statusCell) {
            statusCell.className = `status-badge status-${status === 'accepted' ? 'completed' : status === 'declined' ? 'urgent' : 'pending'}`;
            statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        }

        // Show notification
        const statusMessages = {
            accepted: 'Message accepted successfully',
            declined: 'Message declined',
            replied: 'Reply sent successfully'
        };

        window.notificationSystem.success('Status Updated', statusMessages[status] || 'Status updated');

        // In a real app, send API request here
        console.log(`Message ${messageId} status updated to: ${status}`);
    }

    openReplyModal(messageId = null) {
        const id = messageId || this.currentMessage;
        const messageData = this.getMessageData(id);

        document.getElementById('replyTo').value = messageData.email;
        document.getElementById('replySubject').value = `Re: ${messageData.subject}`;

        window.modalSystem.closeModal('viewMessageModal');
        window.modalSystem.openModal('replyMessageModal');
    }

    sendReply() {
        const form = document.getElementById('replyMessageForm');
        const formData = new FormData(form);
        const replyData = Object.fromEntries(formData.entries());

        // Simulate sending reply
        setTimeout(() => {
            window.modalSystem.closeModal('replyMessageModal');
            
            if (replyData.markAsReplied) {
                this.updateMessageStatus(this.currentMessage, 'replied');
            }
            
            window.notificationSystem.success('Reply Sent', 'Your reply has been sent successfully');
            form.reset();
        }, 500);
    }

    deleteMessage(messageId) {
        window.modalSystem.showConfirmation(
            'Delete Message',
            'Are you sure you want to delete this message?',
            () => {
                // Remove from UI
                const row = document.querySelector(`[data-id="${messageId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }
                
                window.notificationSystem.success('Message Deleted', 'Message has been deleted successfully');
                console.log(`Message ${messageId} deleted`);
            }
        );
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.bulk-checkbox[data-id]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            this.toggleMessageSelection(checkbox.getAttribute('data-id'), checked);
        });
    }

    toggleMessageSelection(messageId, selected) {
        if (selected) {
            this.selectedMessages.add(messageId);
        } else {
            this.selectedMessages.delete(messageId);
        }

        // Update select all checkbox
        const selectAllCheckbox = document.getElementById('selectAll');
        const totalCheckboxes = document.querySelectorAll('.bulk-checkbox[data-id]').length;
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = this.selectedMessages.size === totalCheckboxes;
            selectAllCheckbox.indeterminate = this.selectedMessages.size > 0 && this.selectedMessages.size < totalCheckboxes;
        }

        // Update bulk actions button
        this.updateBulkActionsButton();
    }

    updateBulkActionsButton() {
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        if (bulkActionsBtn) {
            const count = this.selectedMessages.size;
            if (count > 0) {
                bulkActionsBtn.innerHTML = `<i class="fas fa-tasks"></i> Bulk Actions (${count})`;
                bulkActionsBtn.classList.add('btn-primary');
                bulkActionsBtn.classList.remove('btn-outline');
            } else {
                bulkActionsBtn.innerHTML = `<i class="fas fa-tasks"></i> Bulk Actions`;
                bulkActionsBtn.classList.remove('btn-primary');
                bulkActionsBtn.classList.add('btn-outline');
            }
        }
    }

    showBulkActions() {
        if (this.selectedMessages.size === 0) {
            window.notificationSystem.warning('No Selection', 'Please select messages to perform bulk actions');
            return;
        }

        document.getElementById('selectedCount').textContent = this.selectedMessages.size;
        window.modalSystem.openModal('bulkActionsModal');
    }

    bulkUpdateStatus(status) {
        this.selectedMessages.forEach(messageId => {
            this.updateMessageStatus(messageId, status);
        });

        window.modalSystem.closeModal('bulkActionsModal');
        this.clearSelection();
        
        window.notificationSystem.success('Bulk Update', `${this.selectedMessages.size} messages updated to ${status}`);
    }

    bulkDelete() {
        window.modalSystem.showConfirmation(
            'Delete Messages',
            `Are you sure you want to delete ${this.selectedMessages.size} messages?`,
            () => {
                this.selectedMessages.forEach(messageId => {
                    const row = document.querySelector(`[data-id="${messageId}"]`).closest('tr');
                    if (row) row.remove();
                });

                window.notificationSystem.success('Messages Deleted', `${this.selectedMessages.size} messages deleted`);
                this.clearSelection();
            }
        );
    }

    clearSelection() {
        this.selectedMessages.clear();
        document.querySelectorAll('.bulk-checkbox').forEach(cb => cb.checked = false);
        this.updateBulkActionsButton();
    }

    filterByStatus(status) {
        const rows = document.querySelectorAll('.data-table tbody tr');
        
        rows.forEach(row => {
            if (!status) {
                row.style.display = '';
                return;
            }

            const statusBadge = row.querySelector('[data-label="Status"] .status-badge');
            const rowStatus = statusBadge?.textContent.toLowerCase();
            
            row.style.display = rowStatus === status ? '' : 'none';
        });
    }

    initializeFilters() {
        // Initialize any default filters or sorting
    }

    getMessageData(messageId) {
        // Mock data - in real app, fetch from API
        const mockData = {
            '1': {
                name: 'John Smith',
                email: '<EMAIL>',
                subject: 'Website redesign inquiry',
                date: 'Today, 09:42',
                content: 'Hello, I\'m interested in redesigning my company website. We\'re looking for a modern, responsive design that reflects our brand values. Could you please provide more information about your services and pricing?'
            },
            '2': {
                name: 'Sarah Johnson',
                email: '<EMAIL>',
                subject: 'Partnership proposal',
                date: 'Today, 08:15',
                content: 'We would like to discuss a potential partnership opportunity. Our company specializes in digital marketing and we believe there could be synergies with your web development services.'
            }
        };

        return mockData[messageId] || mockData['1'];
    }
}

// Initialize messages manager
document.addEventListener('DOMContentLoaded', () => {
    window.messagesManager = new MessagesManager();
});
