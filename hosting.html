<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hosting Services - DigitalAce Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                DigitalAce
            </a>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-title">Main</div>
            <a href="index.html" class="menu-item">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            
            <div class="menu-title">Management</div>
            <a href="projects.html" class="menu-item">
                <i class="fas fa-project-diagram"></i>
                Projects
            </a>
            <a href="messages.html" class="menu-item">
                <i class="fas fa-comments"></i>
                Client Messages
            </a>
            <a href="support.html" class="menu-item">
                <i class="fas fa-question-circle"></i>
                Support Queries
            </a>
            <a href="users.html" class="menu-item">
                <i class="fas fa-users"></i>
                Users
            </a>
            <a href="hosting.html" class="menu-item active">
                <i class="fas fa-server"></i>
                Hosting Services
            </a>
            <a href="gps-tracker.html" class="menu-item">
                <i class="fas fa-map-marker-alt"></i>
                GPS Trackers
            </a>
            
            <div class="menu-title">Settings</div>
            <a href="settings.html" class="menu-item">
                <i class="fas fa-cog"></i>
                System Settings
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-user-shield"></i>
                Admin Accounts
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <div class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="page-title">Hosting Services</div>
            </div>
            
            <div class="nav-right">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar">AD</div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Super Admin</div>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-bell"></i> Notifications
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Content Container -->
        <div class="content-container">
            <!-- Hosting Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Plans</div>
                        <div class="stat-icon">
                            <i class="fas fa-server"></i>
                        </div>
                    </div>
                    <div class="stat-value">8</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 2 new this month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Active Subscriptions</div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-value">342</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 15% from last month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Monthly Revenue</div>
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value">$24.8K</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 22% from last month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Server Uptime</div>
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="stat-value">99.9%</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 0.1% improvement
                    </div>
                </div>
            </div>
            
            <!-- Hosting Plans Section -->
            <div class="data-section">
                <div class="section-header">
                    <div class="section-title">Hosting Plans</div>
                    <div class="section-actions">
                        <select class="action-btn btn-outline" id="planTypeFilter">
                            <option value="">All Types</option>
                            <option value="shared">Shared Hosting</option>
                            <option value="vps">VPS Hosting</option>
                            <option value="dedicated">Dedicated Server</option>
                            <option value="cloud">Cloud Hosting</option>
                        </select>
                        <select class="action-btn btn-outline" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="maintenance">Maintenance</option>
                        </select>
                        <button class="action-btn btn-outline">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="action-btn btn-primary" id="addPlanBtn">
                            <i class="fas fa-plus"></i> Add Plan
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Plan Name</th>
                                <th>Type</th>
                                <th>Price</th>
                                <th>Storage</th>
                                <th>Bandwidth</th>
                                <th>Domains</th>
                                <th>Subscribers</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td data-label="Plan Name">
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div style="width: 32px; height: 32px; background: var(--primary); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                                            <i class="fas fa-server"></i>
                                        </div>
                                        <div>
                                            <div style="font-weight: 600;">Basic Shared</div>
                                            <div style="font-size: 12px; color: var(--text-secondary);">Entry-level hosting</div>
                                        </div>
                                    </div>
                                </td>
                                <td data-label="Type"><span class="status-badge status-pending">Shared</span></td>
                                <td data-label="Price">$9.99/mo</td>
                                <td data-label="Storage">10 GB</td>
                                <td data-label="Bandwidth">100 GB</td>
                                <td data-label="Domains">1</td>
                                <td data-label="Subscribers">89</td>
                                <td data-label="Status"><span class="status-badge status-completed">Active</span></td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="1" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="edit" data-id="1" title="Edit Plan">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-btn" data-action="features" data-id="1" title="Manage Features">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="table-btn" data-action="toggle" data-id="1" title="Toggle Status">
                                            <i class="fas fa-power-off" style="color: var(--warning);"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="1" title="Delete Plan">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td data-label="Plan Name">
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div style="width: 32px; height: 32px; background: var(--secondary); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                                            <i class="fas fa-cloud"></i>
                                        </div>
                                        <div>
                                            <div style="font-weight: 600;">Pro VPS</div>
                                            <div style="font-size: 12px; color: var(--text-secondary);">Virtual private server</div>
                                        </div>
                                    </div>
                                </td>
                                <td data-label="Type"><span class="status-badge status-warning">VPS</span></td>
                                <td data-label="Price">$49.99/mo</td>
                                <td data-label="Storage">100 GB SSD</td>
                                <td data-label="Bandwidth">1 TB</td>
                                <td data-label="Domains">Unlimited</td>
                                <td data-label="Subscribers">156</td>
                                <td data-label="Status"><span class="status-badge status-completed">Active</span></td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="2" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="edit" data-id="2" title="Edit Plan">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-btn" data-action="features" data-id="2" title="Manage Features">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="table-btn" data-action="toggle" data-id="2" title="Toggle Status">
                                            <i class="fas fa-power-off" style="color: var(--warning);"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="2" title="Delete Plan">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- View Plan Modal -->
    <div class="modal-backdrop" id="viewPlanModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-server"></i>
                    Plan Details - <span id="planName">Basic Shared</span>
                </h3>
                <button class="modal-close" data-modal="viewPlanModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="plan-details">
                    <div class="plan-header" style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
                        <div id="planIcon" style="width: 60px; height: 60px; background: var(--primary); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 24px;">
                            <i class="fas fa-server"></i>
                        </div>
                        <div>
                            <h4 id="planTitle">Basic Shared Hosting</h4>
                            <p style="color: var(--text-secondary); margin: 0;">Perfect for small websites and blogs</p>
                            <p style="color: var(--primary); font-size: 24px; font-weight: bold; margin: 5px 0 0 0;" id="planPrice">$9.99/month</p>
                        </div>
                    </div>

                    <div class="plan-info-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px;">
                        <div class="plan-info-item">
                            <strong>Storage:</strong> <span id="planStorage">10 GB</span>
                        </div>
                        <div class="plan-info-item">
                            <strong>Bandwidth:</strong> <span id="planBandwidth">100 GB</span>
                        </div>
                        <div class="plan-info-item">
                            <strong>Domains:</strong> <span id="planDomains">1</span>
                        </div>
                        <div class="plan-info-item">
                            <strong>Email Accounts:</strong> <span id="planEmails">5</span>
                        </div>
                        <div class="plan-info-item">
                            <strong>Databases:</strong> <span id="planDatabases">1</span>
                        </div>
                        <div class="plan-info-item">
                            <strong>SSL Certificate:</strong> <span id="planSSL">Free</span>
                        </div>
                    </div>

                    <div class="plan-features">
                        <strong>Features:</strong>
                        <ul id="planFeaturesList" style="margin-top: 10px; padding-left: 20px;">
                            <li>24/7 Customer Support</li>
                            <li>99.9% Uptime Guarantee</li>
                            <li>Free Website Builder</li>
                            <li>Daily Backups</li>
                            <li>cPanel Control Panel</li>
                        </ul>
                    </div>

                    <div class="plan-stats" style="margin-top: 20px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                            <div class="stat-item">
                                <div style="font-size: 24px; font-weight: bold; color: var(--primary);" id="planSubscribers">89</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Active Subscribers</div>
                            </div>
                            <div class="stat-item">
                                <div style="font-size: 24px; font-weight: bold; color: var(--secondary);" id="planRevenue">$890</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Monthly Revenue</div>
                            </div>
                            <div class="stat-item">
                                <div style="font-size: 24px; font-weight: bold; color: var(--success);" id="planUptime">99.9%</div>
                                <div style="font-size: 12px; color: var(--text-secondary);">Uptime</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" id="editPlanFromView">
                    <i class="fas fa-edit"></i>
                    Edit Plan
                </button>
                <button class="modal-btn modal-btn-primary" id="manageFeaturesFromView">
                    <i class="fas fa-list"></i>
                    Manage Features
                </button>
            </div>
        </div>
    </div>

    <!-- Add/Edit Plan Modal -->
    <div class="modal-backdrop" id="planModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus"></i>
                    <span id="planModalTitle">Add New Plan</span>
                </h3>
                <button class="modal-close" data-modal="planModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="planForm">
                    <div class="form-group">
                        <label class="form-label">Plan Name</label>
                        <input type="text" class="form-input" name="planName" placeholder="Enter plan name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Plan Type</label>
                        <select class="form-input form-select" name="planType" required>
                            <option value="">Select plan type</option>
                            <option value="shared">Shared Hosting</option>
                            <option value="vps">VPS Hosting</option>
                            <option value="dedicated">Dedicated Server</option>
                            <option value="cloud">Cloud Hosting</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <input type="text" class="form-input" name="description" placeholder="Brief description">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Price (Monthly)</label>
                        <input type="number" class="form-input" name="price" placeholder="0.00" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Storage</label>
                        <input type="text" class="form-input" name="storage" placeholder="e.g., 10 GB, 100 GB SSD" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Bandwidth</label>
                        <input type="text" class="form-input" name="bandwidth" placeholder="e.g., 100 GB, Unlimited" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Domains Allowed</label>
                        <input type="text" class="form-input" name="domains" placeholder="e.g., 1, 5, Unlimited" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Email Accounts</label>
                        <input type="text" class="form-input" name="emails" placeholder="e.g., 5, 50, Unlimited">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Databases</label>
                        <input type="text" class="form-input" name="databases" placeholder="e.g., 1, 10, Unlimited">
                    </div>
                    <div class="form-group">
                        <label class="form-label">SSL Certificate</label>
                        <select class="form-input form-select" name="ssl">
                            <option value="free">Free SSL</option>
                            <option value="premium">Premium SSL</option>
                            <option value="wildcard">Wildcard SSL</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="planModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="savePlanBtn">
                    <i class="fas fa-save"></i>
                    <span id="savePlanText">Create Plan</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Manage Features Modal -->
    <div class="modal-backdrop" id="featuresModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-list"></i>
                    Manage Plan Features
                </h3>
                <button class="modal-close" data-modal="featuresModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="features-manager">
                    <div class="current-features">
                        <h4>Current Features</h4>
                        <div id="currentFeaturesList" class="features-list">
                            <div class="feature-item">
                                <span>24/7 Customer Support</span>
                                <button class="remove-feature-btn" data-feature="support">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="feature-item">
                                <span>99.9% Uptime Guarantee</span>
                                <button class="remove-feature-btn" data-feature="uptime">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="add-feature" style="margin-top: 20px;">
                        <h4>Add New Feature</h4>
                        <div style="display: flex; gap: 10px; margin-top: 10px;">
                            <input type="text" class="form-input" id="newFeatureInput" placeholder="Enter feature name" style="flex: 1;">
                            <button class="modal-btn modal-btn-primary" id="addFeatureBtn">
                                <i class="fas fa-plus"></i>
                                Add
                            </button>
                        </div>
                    </div>

                    <div class="predefined-features" style="margin-top: 20px;">
                        <h4>Quick Add Features</h4>
                        <div class="quick-features" style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
                            <button class="quick-feature-btn" data-feature="Free Website Builder">Website Builder</button>
                            <button class="quick-feature-btn" data-feature="Daily Backups">Daily Backups</button>
                            <button class="quick-feature-btn" data-feature="cPanel Control Panel">cPanel</button>
                            <button class="quick-feature-btn" data-feature="Free Domain">Free Domain</button>
                            <button class="quick-feature-btn" data-feature="SSD Storage">SSD Storage</button>
                            <button class="quick-feature-btn" data-feature="CDN Integration">CDN</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="featuresModal">Close</button>
                <button class="modal-btn modal-btn-primary" id="saveFeaturesBtn">
                    <i class="fas fa-save"></i>
                    Save Features
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-backdrop" id="deleteModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Confirm Delete
                </h3>
                <button class="modal-close" data-modal="deleteModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation danger">
                    <div class="modal-icon">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                    <div class="modal-message">Are you sure you want to delete this hosting plan?</div>
                    <div class="modal-description">This action cannot be undone. All subscribers will need to be migrated to other plans.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="deleteModal">Cancel</button>
                <button class="modal-btn modal-btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    Delete Plan
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    <script src="./assets/js/index.js"></script>
    <script src="./assets/js/hosting.js"></script>
</body>
</html>
