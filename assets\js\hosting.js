/* ===================================
   HOSTING SERVICES PAGE FUNCTIONALITY
   ===================================
   
   Handles hosting plan management, CRUD operations,
   feature management, and plan interactions.
*/

class HostingManager {
    constructor() {
        this.currentPlan = null;
        this.currentFeatures = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeFilters();
    }

    bindEvents() {
        // Plan action buttons
        document.addEventListener('click', (e) => {
            const action = e.target.closest('[data-action]')?.getAttribute('data-action');
            const planId = e.target.closest('[data-id]')?.getAttribute('data-id');

            if (action && planId) {
                this.handlePlanAction(action, planId);
            }
        });

        // Add plan button
        const addPlanBtn = document.getElementById('addPlanBtn');
        if (addPlanBtn) {
            addPlanBtn.addEventListener('click', () => {
                this.openAddPlanModal();
            });
        }

        // Filters
        const planTypeFilter = document.getElementById('planTypeFilter');
        const statusFilter = document.getElementById('statusFilter');

        if (planTypeFilter) {
            planTypeFilter.addEventListener('change', (e) => {
                this.filterPlans('type', e.target.value);
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterPlans('status', e.target.value);
            });
        }

        // Modal action buttons
        this.bindModalActions();
    }

    bindModalActions() {
        // Save plan
        const savePlanBtn = document.getElementById('savePlanBtn');
        if (savePlanBtn) {
            savePlanBtn.addEventListener('click', () => {
                this.savePlan();
            });
        }

        // View modal actions
        const editPlanFromView = document.getElementById('editPlanFromView');
        const manageFeaturesFromView = document.getElementById('manageFeaturesFromView');

        if (editPlanFromView) {
            editPlanFromView.addEventListener('click', () => {
                this.editPlanFromView();
            });
        }

        if (manageFeaturesFromView) {
            manageFeaturesFromView.addEventListener('click', () => {
                this.manageFeaturesFromView();
            });
        }

        // Features management
        const addFeatureBtn = document.getElementById('addFeatureBtn');
        const saveFeaturesBtn = document.getElementById('saveFeaturesBtn');

        if (addFeatureBtn) {
            addFeatureBtn.addEventListener('click', () => {
                this.addCustomFeature();
            });
        }

        if (saveFeaturesBtn) {
            saveFeaturesBtn.addEventListener('click', () => {
                this.saveFeatures();
            });
        }

        // Quick feature buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-feature-btn')) {
                const feature = e.target.getAttribute('data-feature');
                this.addQuickFeature(feature);
            }

            if (e.target.classList.contains('remove-feature-btn')) {
                const feature = e.target.getAttribute('data-feature');
                this.removeFeature(feature);
            }
        });
    }

    handlePlanAction(action, planId) {
        this.currentPlan = planId;

        switch (action) {
            case 'view':
                this.viewPlan(planId);
                break;
            case 'edit':
                this.editPlan(planId);
                break;
            case 'features':
                this.manageFeatures(planId);
                break;
            case 'toggle':
                this.togglePlanStatus(planId);
                break;
            case 'delete':
                this.deletePlan(planId);
                break;
        }
    }

    viewPlan(planId) {
        const planData = this.getPlanData(planId);
        
        // Populate modal with plan data
        document.getElementById('planName').textContent = planData.name;
        document.getElementById('planTitle').textContent = planData.fullName;
        document.getElementById('planPrice').textContent = `$${planData.price}/month`;
        document.getElementById('planStorage').textContent = planData.storage;
        document.getElementById('planBandwidth').textContent = planData.bandwidth;
        document.getElementById('planDomains').textContent = planData.domains;
        document.getElementById('planEmails').textContent = planData.emails;
        document.getElementById('planDatabases').textContent = planData.databases;
        document.getElementById('planSSL').textContent = planData.ssl;
        document.getElementById('planSubscribers').textContent = planData.subscribers;
        document.getElementById('planRevenue').textContent = `$${planData.revenue}`;
        document.getElementById('planUptime').textContent = planData.uptime;

        // Update icon
        const iconElement = document.getElementById('planIcon');
        iconElement.style.background = planData.iconColor;
        iconElement.innerHTML = `<i class="${planData.icon}"></i>`;

        // Update features list
        const featuresList = document.getElementById('planFeaturesList');
        featuresList.innerHTML = planData.features.map(feature => `<li>${feature}</li>`).join('');

        window.modalSystem.openModal('viewPlanModal');
    }

    openAddPlanModal() {
        this.currentPlan = null;
        document.getElementById('planModalTitle').textContent = 'Add New Plan';
        document.getElementById('savePlanText').textContent = 'Create Plan';
        document.getElementById('planForm').reset();
        window.modalSystem.openModal('planModal');
    }

    editPlan(planId) {
        const planData = this.getPlanData(planId);
        const form = document.getElementById('planForm');
        
        // Populate form with plan data
        form.planName.value = planData.name;
        form.planType.value = planData.type.toLowerCase();
        form.description.value = planData.description;
        form.price.value = planData.price;
        form.storage.value = planData.storage;
        form.bandwidth.value = planData.bandwidth;
        form.domains.value = planData.domains;
        form.emails.value = planData.emails;
        form.databases.value = planData.databases;
        form.ssl.value = planData.ssl.toLowerCase();

        document.getElementById('planModalTitle').textContent = 'Edit Plan';
        document.getElementById('savePlanText').textContent = 'Save Changes';
        window.modalSystem.openModal('planModal');
    }

    editPlanFromView() {
        window.modalSystem.closeModal('viewPlanModal');
        this.editPlan(this.currentPlan);
    }

    manageFeaturesFromView() {
        window.modalSystem.closeModal('viewPlanModal');
        this.manageFeatures(this.currentPlan);
    }

    manageFeatures(planId) {
        const planData = this.getPlanData(planId);
        this.currentFeatures = [...planData.features];
        this.updateFeaturesDisplay();
        window.modalSystem.openModal('featuresModal');
    }

    updateFeaturesDisplay() {
        const container = document.getElementById('currentFeaturesList');
        container.innerHTML = this.currentFeatures.map((feature, index) => `
            <div class="feature-item">
                <span>${feature}</span>
                <button class="remove-feature-btn" data-feature="${index}">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }

    addCustomFeature() {
        const input = document.getElementById('newFeatureInput');
        const feature = input.value.trim();
        
        if (feature && !this.currentFeatures.includes(feature)) {
            this.currentFeatures.push(feature);
            this.updateFeaturesDisplay();
            input.value = '';
        }
    }

    addQuickFeature(feature) {
        if (!this.currentFeatures.includes(feature)) {
            this.currentFeatures.push(feature);
            this.updateFeaturesDisplay();
        }
    }

    removeFeature(featureIndex) {
        this.currentFeatures.splice(parseInt(featureIndex), 1);
        this.updateFeaturesDisplay();
    }

    saveFeatures() {
        window.modalSystem.closeModal('featuresModal');
        window.notificationSystem.success('Features Updated', 'Plan features have been updated successfully');
        console.log('Features saved:', this.currentFeatures);
    }

    savePlan() {
        const form = document.getElementById('planForm');
        const formData = new FormData(form);
        const planData = Object.fromEntries(formData.entries());

        // Validate required fields
        if (!planData.planName || !planData.planType || !planData.price || !planData.storage || !planData.bandwidth || !planData.domains) {
            window.notificationSystem.error('Validation Error', 'Please fill in all required fields');
            return;
        }

        // Simulate API call
        setTimeout(() => {
            window.modalSystem.closeModal('planModal');
            
            if (this.currentPlan) {
                window.notificationSystem.success('Plan Updated', 'Hosting plan has been updated successfully');
                this.updatePlanInTable(this.currentPlan, planData);
            } else {
                window.notificationSystem.success('Plan Created', 'New hosting plan has been created successfully');
            }
            
            console.log('Plan saved:', planData);
        }, 500);
    }

    updatePlanInTable(planId, planData) {
        const row = document.querySelector(`[data-id="${planId}"]`).closest('tr');
        if (!row) return;

        // Update plan name
        const nameCell = row.querySelector('[data-label="Plan Name"] div div');
        if (nameCell) {
            nameCell.textContent = planData.planName;
        }

        // Update type
        const typeCell = row.querySelector('[data-label="Type"] .status-badge');
        if (typeCell) {
            typeCell.textContent = planData.planType.charAt(0).toUpperCase() + planData.planType.slice(1);
        }

        // Update price
        const priceCell = row.querySelector('[data-label="Price"]');
        if (priceCell) {
            priceCell.textContent = `$${planData.price}/mo`;
        }

        // Update storage
        const storageCell = row.querySelector('[data-label="Storage"]');
        if (storageCell) {
            storageCell.textContent = planData.storage;
        }

        // Update bandwidth
        const bandwidthCell = row.querySelector('[data-label="Bandwidth"]');
        if (bandwidthCell) {
            bandwidthCell.textContent = planData.bandwidth;
        }

        // Update domains
        const domainsCell = row.querySelector('[data-label="Domains"]');
        if (domainsCell) {
            domainsCell.textContent = planData.domains;
        }
    }

    togglePlanStatus(planId) {
        const row = document.querySelector(`[data-id="${planId}"]`).closest('tr');
        const statusCell = row?.querySelector('[data-label="Status"] .status-badge');
        
        if (statusCell) {
            const currentStatus = statusCell.textContent.toLowerCase();
            const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
            
            statusCell.className = `status-badge status-${newStatus === 'active' ? 'completed' : 'urgent'}`;
            statusCell.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);
            
            window.notificationSystem.success('Status Updated', `Plan has been ${newStatus === 'active' ? 'activated' : 'deactivated'}`);
        }
    }

    deletePlan(planId) {
        window.modalSystem.showConfirmation(
            'Delete Hosting Plan',
            'Are you sure you want to delete this hosting plan?',
            () => {
                // Remove from UI
                const row = document.querySelector(`[data-id="${planId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }
                
                window.notificationSystem.success('Plan Deleted', 'Hosting plan has been deleted successfully');
                console.log(`Plan ${planId} deleted`);
            }
        );
    }

    filterPlans(filterType, value) {
        const rows = document.querySelectorAll('.data-table tbody tr');
        
        rows.forEach(row => {
            if (!value) {
                row.style.display = '';
                return;
            }

            let cellSelector;
            if (filterType === 'type') {
                cellSelector = '[data-label="Type"] .status-badge';
            } else if (filterType === 'status') {
                cellSelector = '[data-label="Status"] .status-badge';
            }

            const cell = row.querySelector(cellSelector);
            const cellValue = cell?.textContent.toLowerCase();
            
            row.style.display = cellValue === value ? '' : 'none';
        });
    }

    initializeFilters() {
        // Initialize any default filters or sorting
    }

    getPlanData(planId) {
        // Mock data - in real app, fetch from API
        const mockData = {
            '1': {
                name: 'Basic Shared',
                fullName: 'Basic Shared Hosting',
                type: 'Shared',
                description: 'Entry-level hosting',
                price: '9.99',
                storage: '10 GB',
                bandwidth: '100 GB',
                domains: '1',
                emails: '5',
                databases: '1',
                ssl: 'Free',
                subscribers: '89',
                revenue: '890',
                uptime: '99.9%',
                icon: 'fas fa-server',
                iconColor: 'var(--primary)',
                features: [
                    '24/7 Customer Support',
                    '99.9% Uptime Guarantee',
                    'Free Website Builder',
                    'Daily Backups',
                    'cPanel Control Panel'
                ]
            },
            '2': {
                name: 'Pro VPS',
                fullName: 'Pro VPS Hosting',
                type: 'VPS',
                description: 'Virtual private server',
                price: '49.99',
                storage: '100 GB SSD',
                bandwidth: '1 TB',
                domains: 'Unlimited',
                emails: '50',
                databases: '10',
                ssl: 'Premium',
                subscribers: '156',
                revenue: '7800',
                uptime: '99.95%',
                icon: 'fas fa-cloud',
                iconColor: 'var(--secondary)',
                features: [
                    '24/7 Priority Support',
                    '99.95% Uptime Guarantee',
                    'SSD Storage',
                    'Root Access',
                    'Free SSL Certificate',
                    'Daily Backups',
                    'CDN Integration'
                ]
            }
        };

        return mockData[planId] || mockData['1'];
    }
}

// Initialize hosting manager
document.addEventListener('DOMContentLoaded', () => {
    window.hostingManager = new HostingManager();
});
