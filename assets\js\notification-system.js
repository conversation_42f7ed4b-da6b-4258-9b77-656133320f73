/* ===================================
   NOTIFICATION SYSTEM JAVASCRIPT
   ===================================
   
   Handles toast notifications with different types,
   auto-dismiss, and queue management.
*/

class NotificationSystem {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        this.init();
    }

    init() {
        this.createContainer();
        this.bindEvents();
    }

    createContainer() {
        this.container = document.getElementById('notificationContainer');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'notificationContainer';
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
        }
    }

    bindEvents() {
        // Handle notification close buttons
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('notification-close') || 
                e.target.closest('.notification-close')) {
                const notification = e.target.closest('.notification');
                if (notification) {
                    this.removeNotification(notification);
                }
            }

            // Handle notification action buttons
            if (e.target.classList.contains('notification-action')) {
                const action = e.target.getAttribute('data-action');
                const notification = e.target.closest('.notification');
                this.handleAction(action, notification);
            }
        });
    }

    show(type = 'info', title = '', message = '', options = {}) {
        const notification = this.createNotification(type, title, message, options);
        this.addNotification(notification);
        return notification;
    }

    createNotification(type, title, message, options) {
        const {
            duration = this.defaultDuration,
            actions = [],
            persistent = false,
            size = 'normal'
        } = options;

        const notification = document.createElement('div');
        notification.className = `notification ${type} ${size}`;
        notification.setAttribute('data-type', type);

        const iconMap = {
            success: 'fas fa-check',
            error: 'fas fa-times',
            warning: 'fas fa-exclamation',
            info: 'fas fa-info'
        };

        const icon = iconMap[type] || iconMap.info;

        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${icon}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
                ${actions.length > 0 ? this.createActions(actions) : ''}
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
            ${!persistent ? '<div class="notification-progress"><div class="notification-progress-bar"></div></div>' : ''}
        `;

        // Auto-dismiss if not persistent
        if (!persistent && duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification);
            }, duration);
        }

        return notification;
    }

    createActions(actions) {
        const actionsHtml = actions.map(action => `
            <button class="notification-action ${action.type || 'secondary'}" 
                    data-action="${action.action || ''}"
                    ${action.href ? `onclick="window.open('${action.href}', '_blank')"` : ''}>
                ${action.icon ? `<i class="${action.icon}"></i>` : ''}
                ${action.text}
            </button>
        `).join('');

        return `<div class="notification-actions">${actionsHtml}</div>`;
    }

    addNotification(notification) {
        // Remove oldest notification if at max capacity
        if (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications[0];
            this.removeNotification(oldest);
        }

        this.container.appendChild(notification);
        this.notifications.push(notification);

        // Trigger animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }

    removeNotification(notification) {
        if (!notification || !notification.parentNode) return;

        notification.classList.add('hide');
        notification.classList.remove('show');

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications = this.notifications.filter(n => n !== notification);
        }, 300);
    }

    handleAction(action, notification) {
        console.log('Notification action:', action);
        
        // Handle common actions
        switch (action) {
            case 'dismiss':
                this.removeNotification(notification);
                break;
            case 'undo':
                // Handle undo action
                this.removeNotification(notification);
                break;
            default:
                // Custom action handling
                break;
        }
    }

    // Convenience methods
    success(title, message, options = {}) {
        return this.show('success', title, message, options);
    }

    error(title, message, options = {}) {
        return this.show('error', title, message, options);
    }

    warning(title, message, options = {}) {
        return this.show('warning', title, message, options);
    }

    info(title, message, options = {}) {
        return this.show('info', title, message, options);
    }

    // Clear all notifications
    clear() {
        this.notifications.forEach(notification => {
            this.removeNotification(notification);
        });
    }

    // Show notification with actions
    showWithActions(type, title, message, actions, options = {}) {
        return this.show(type, title, message, { ...options, actions });
    }
}

// Initialize notification system
document.addEventListener('DOMContentLoaded', () => {
    window.notificationSystem = new NotificationSystem();
    
    // Demo notification buttons
    const demoButtons = {
        successNotificationBtn: () => {
            window.notificationSystem.success(
                'Success!', 
                'Operation completed successfully. All changes have been saved.',
                { 
                    actions: [
                        { text: 'View Details', type: 'primary', action: 'view' },
                        { text: 'Dismiss', type: 'secondary', action: 'dismiss' }
                    ]
                }
            );
        },
        errorNotificationBtn: () => {
            window.notificationSystem.error(
                'Error Occurred', 
                'Failed to save changes. Please check your connection and try again.',
                { 
                    persistent: true,
                    actions: [
                        { text: 'Retry', type: 'primary', action: 'retry' },
                        { text: 'Report Issue', type: 'secondary', action: 'report' }
                    ]
                }
            );
        },
        warningNotificationBtn: () => {
            window.notificationSystem.warning(
                'Warning', 
                'Your session will expire in 5 minutes. Please save your work.',
                { 
                    duration: 8000,
                    actions: [
                        { text: 'Extend Session', type: 'primary', action: 'extend' }
                    ]
                }
            );
        },
        infoNotificationBtn: () => {
            window.notificationSystem.info(
                'New Update Available', 
                'Version 2.1.0 is now available with new features and improvements.',
                { 
                    actions: [
                        { text: 'Update Now', type: 'primary', action: 'update' },
                        { text: 'Later', type: 'secondary', action: 'dismiss' }
                    ]
                }
            );
        }
    };

    // Bind demo buttons
    Object.keys(demoButtons).forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.addEventListener('click', demoButtons[buttonId]);
        }
    });
});
