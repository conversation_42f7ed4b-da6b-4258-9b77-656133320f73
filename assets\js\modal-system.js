/* ===================================
   MODAL SYSTEM JAVASCRIPT
   ===================================
   
   Handles modal functionality, animations,
   and interactions for the admin panel.
*/

class ModalSystem {
    constructor() {
        this.activeModal = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.handleEscapeKey();
    }

    bindEvents() {
        // Modal trigger buttons
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-modal-trigger]');
            if (trigger) {
                const modalId = trigger.getAttribute('data-modal-trigger');
                this.openModal(modalId);
            }

            // Modal close buttons
            const closeBtn = e.target.closest('[data-modal]');
            if (closeBtn) {
                const modalId = closeBtn.getAttribute('data-modal');
                this.closeModal(modalId);
            }

            // Close modal when clicking backdrop
            if (e.target.classList.contains('modal-backdrop')) {
                this.closeModal(e.target.id);
            }
        });

        // Form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('modal-form')) {
                // Allow demoForm to submit normally to server
                if (e.target.id === 'demoForm') {
                    return; // Let the form submit naturally
                }
                e.preventDefault();
                this.handleFormSubmission(e.target);
            }
        });
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        // Close any existing modal first
        if (this.activeModal) {
            this.closeModal(this.activeModal);
        }

        modal.classList.add('active');
        this.activeModal = modalId;
        document.body.style.overflow = 'hidden';

        // Add animation class
        const modalElement = modal.querySelector('.modal');
        if (modalElement) {
            modalElement.classList.add('animate-in');
            setTimeout(() => {
                modalElement.classList.remove('animate-in');
            }, 300);
        }

        // Focus first input
        const firstInput = modal.querySelector('input, textarea, select');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        const modalElement = modal.querySelector('.modal');
        if (modalElement) {
            modalElement.classList.add('animate-out');
            setTimeout(() => {
                modal.classList.remove('active');
                modalElement.classList.remove('animate-out');
                document.body.style.overflow = '';
                this.activeModal = null;
                this.resetForm(modal);
            }, 200);
        } else {
            modal.classList.remove('active');
            document.body.style.overflow = '';
            this.activeModal = null;
            this.resetForm(modal);
        }
    }

    handleEscapeKey() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal(this.activeModal);
            }
        });
    }

    resetForm(modal) {
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }

    handleFormSubmission(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Get form type
        const formId = form.id;
        
        switch (formId) {
            case 'newProjectForm':
                this.handleNewProject(data);
                break;
            case 'newTicketForm':
                this.handleNewTicket(data);
                break;
            default:
                console.log('Form submitted:', data);
        }
    }

    handleNewProject(data) {
        // Simulate API call
        setTimeout(() => {
            this.closeModal('newProjectModal');
            window.notificationSystem.show('success', 'Project Created', 'New project has been created successfully!');
            
            // In a real app, you would refresh the projects table here
            console.log('New project created:', data);
        }, 500);
    }

    handleNewTicket(data) {
        // Simulate API call
        setTimeout(() => {
            this.closeModal('newTicketModal');
            window.notificationSystem.show('success', 'Ticket Created', 'Support ticket has been created successfully!');
            
            // In a real app, you would refresh the tickets table here
            console.log('New ticket created:', data);
        }, 500);
    }

    // Confirmation modal helper
    showConfirmation(title, message, onConfirm, type = 'warning') {
        const modal = document.getElementById('deleteModal');
        if (!modal) return;

        // Update modal content
        const titleElement = modal.querySelector('.modal-title');
        const messageElement = modal.querySelector('.modal-message');
        const descriptionElement = modal.querySelector('.modal-description');
        const confirmBtn = modal.querySelector('#confirmDeleteBtn');

        if (titleElement) titleElement.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${title}`;
        if (messageElement) messageElement.textContent = message;
        if (descriptionElement) descriptionElement.textContent = 'This action cannot be undone.';

        // Set up confirmation handler
        const handleConfirm = () => {
            onConfirm();
            this.closeModal('deleteModal');
            confirmBtn.removeEventListener('click', handleConfirm);
        };

        confirmBtn.addEventListener('click', handleConfirm);
        this.openModal('deleteModal');
    }
}

// Initialize modal system
document.addEventListener('DOMContentLoaded', () => {
    window.modalSystem = new ModalSystem();
});
