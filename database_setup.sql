-- Database setup for popup form
-- Create database (if it doesn't exist)
CREATE DATABASE IF NOT EXISTS your_database_name;
USE your_database_name;

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    newsletter TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Optional: Insert some sample data for testing
-- INSERT INTO products (full_name, email, phone, subject, message, newsletter) 
-- VALUES ('<PERSON>', '<EMAIL>', '************', 'general', 'This is a test message', 1);
