<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - DigitalAce Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                DigitalAce
            </a>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-title">Main</div>
            <a href="index.html" class="menu-item">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            
            <div class="menu-title">Management</div>
            <a href="projects.html" class="menu-item">
                <i class="fas fa-project-diagram"></i>
                Projects
            </a>
            <a href="messages.html" class="menu-item">
                <i class="fas fa-comments"></i>
                Client Messages
            </a>
            <a href="support.html" class="menu-item">
                <i class="fas fa-question-circle"></i>
                Support Queries
            </a>
            <a href="users.html" class="menu-item">
                <i class="fas fa-users"></i>
                Users
            </a>
            <a href="hosting.html" class="menu-item">
                <i class="fas fa-server"></i>
                Hosting Services
            </a>
            <a href="gps-tracker.html" class="menu-item">
                <i class="fas fa-map-marker-alt"></i>
                GPS Trackers
            </a>
            
            <div class="menu-title">Settings</div>
            <a href="settings.html" class="menu-item active">
                <i class="fas fa-cog"></i>
                System Settings
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-user-shield"></i>
                Admin Accounts
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <div class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="page-title">System Settings</div>
            </div>
            
            <div class="nav-right">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar">AD</div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Super Admin</div>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-bell"></i> Notifications
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Content Container -->
        <div class="content-container">
            <!-- Settings Grid -->
            <div class="settings-grid">
                <div class="settings-card">
                    <div class="settings-card-title">
                        <i class="fas fa-palette"></i>
                        Theme Settings
                    </div>
                    <div class="settings-card-description">
                        Customize the appearance and theme of your admin panel.
                    </div>
                    <div class="settings-toggle">
                        <span>Dark Mode</span>
                        <div class="toggle-switch" id="darkModeToggle"></div>
                    </div>
                    <div class="settings-toggle">
                        <span>Auto Theme</span>
                        <div class="toggle-switch" id="autoThemeToggle"></div>
                    </div>
                </div>

                <div class="settings-card">
                    <div class="settings-card-title">
                        <i class="fas fa-bell"></i>
                        Notifications
                    </div>
                    <div class="settings-card-description">
                        Configure notification preferences and alerts.
                    </div>
                    <div class="settings-toggle">
                        <span>Email Notifications</span>
                        <div class="toggle-switch active" id="emailNotificationsToggle"></div>
                    </div>
                    <div class="settings-toggle">
                        <span>Push Notifications</span>
                        <div class="toggle-switch active" id="pushNotificationsToggle"></div>
                    </div>
                    <div class="settings-toggle">
                        <span>Sound Alerts</span>
                        <div class="toggle-switch" id="soundAlertsToggle"></div>
                    </div>
                </div>

                <div class="settings-card">
                    <div class="settings-card-title">
                        <i class="fas fa-shield-alt"></i>
                        Security
                    </div>
                    <div class="settings-card-description">
                        Manage security settings and access controls.
                    </div>
                    <div class="settings-toggle">
                        <span>Two-Factor Auth</span>
                        <div class="toggle-switch active" id="twoFactorToggle"></div>
                    </div>
                    <div class="settings-toggle">
                        <span>Session Timeout</span>
                        <div class="toggle-switch active" id="sessionTimeoutToggle"></div>
                    </div>
                </div>

                <div class="settings-card">
                    <div class="settings-card-title">
                        <i class="fas fa-database"></i>
                        Data Management
                    </div>
                    <div class="settings-card-description">
                        Configure data backup and storage settings.
                    </div>
                    <div class="settings-toggle">
                        <span>Auto Backup</span>
                        <div class="toggle-switch active" id="autoBackupToggle"></div>
                    </div>
                    <div class="settings-toggle">
                        <span>Data Compression</span>
                        <div class="toggle-switch" id="dataCompressionToggle"></div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="data-section">
                <div class="section-header">
                    <div class="section-title">System Actions</div>
                    <div class="section-actions">
                        <button class="action-btn btn-outline" id="testNotificationBtn">
                            <i class="fas fa-bell"></i> Test Notification
                        </button>
                        <button class="action-btn btn-outline" id="exportSettingsBtn">
                            <i class="fas fa-download"></i> Export Settings
                        </button>
                        <button class="action-btn btn-primary" id="saveSettingsBtn">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="data-section">
                <div class="section-header">
                    <div class="section-title">System Information</div>
                </div>
                
                <div class="table-responsive">
                    <table class="data-table">
                        <tbody>
                            <tr>
                                <td><strong>System Version</strong></td>
                                <td>DigitalAce Admin v2.1.0</td>
                            </tr>
                            <tr>
                                <td><strong>Last Updated</strong></td>
                                <td>June 25, 2025</td>
                            </tr>
                            <tr>
                                <td><strong>Database Version</strong></td>
                                <td>MySQL 8.0.33</td>
                            </tr>
                            <tr>
                                <td><strong>Server Status</strong></td>
                                <td><span class="status-badge status-completed">Online</span></td>
                            </tr>
                            <tr>
                                <td><strong>Storage Used</strong></td>
                                <td>2.4 GB / 10 GB</td>
                            </tr>
                            <tr>
                                <td><strong>Active Users</strong></td>
                                <td>1,248</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Settings Confirmation Modal -->
    <div class="modal-backdrop" id="settingsModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-save"></i>
                    Save Settings
                </h3>
                <button class="modal-close" data-modal="settingsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation success">
                    <div class="modal-icon">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="modal-message">Save Settings Changes?</div>
                    <div class="modal-description">Your settings will be applied immediately and saved to your profile.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="settingsModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="confirmSaveBtn">
                    <i class="fas fa-save"></i>
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    <script src="./assets/js/index.js"></script>
    <script>
        // Settings page specific functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Toggle switches
            const toggles = document.querySelectorAll('.toggle-switch');
            toggles.forEach(toggle => {
                toggle.addEventListener('click', () => {
                    toggle.classList.toggle('active');
                });
            });

            // Settings buttons
            const testNotificationBtn = document.getElementById('testNotificationBtn');
            const saveSettingsBtn = document.getElementById('saveSettingsBtn');
            const confirmSaveBtn = document.getElementById('confirmSaveBtn');

            if (testNotificationBtn) {
                testNotificationBtn.addEventListener('click', () => {
                    window.notificationSystem.info('Test Notification', 'This is a test notification to verify your settings are working correctly.');
                });
            }

            if (saveSettingsBtn) {
                saveSettingsBtn.addEventListener('click', () => {
                    window.modalSystem.openModal('settingsModal');
                });
            }

            if (confirmSaveBtn) {
                confirmSaveBtn.addEventListener('click', () => {
                    window.modalSystem.closeModal('settingsModal');
                    window.notificationSystem.success('Settings Saved', 'Your settings have been saved successfully!');
                });
            }
        });
    </script>
</body>
</html>
