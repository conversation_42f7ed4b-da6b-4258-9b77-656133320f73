<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Messages - DigitalAce Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                DigitalAce
            </a>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-title">Main</div>
            <a href="index.html" class="menu-item">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            
            <div class="menu-title">Management</div>
            <a href="projects.html" class="menu-item">
                <i class="fas fa-project-diagram"></i>
                Projects
            </a>
            <a href="messages.html" class="menu-item active">
                <i class="fas fa-comments"></i>
                Client Messages
            </a>
            <a href="support.html" class="menu-item">
                <i class="fas fa-question-circle"></i>
                Support Queries
            </a>
            <a href="users.html" class="menu-item">
                <i class="fas fa-users"></i>
                Users
            </a>
            <a href="hosting.html" class="menu-item">
                <i class="fas fa-server"></i>
                Hosting Services
            </a>
            <a href="gps-tracker.html" class="menu-item">
                <i class="fas fa-map-marker-alt"></i>
                GPS Trackers
            </a>
            
            <div class="menu-title">Settings</div>
            <a href="settings.html" class="menu-item">
                <i class="fas fa-cog"></i>
                System Settings
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-user-shield"></i>
                Admin Accounts
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <div class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="page-title">Client Messages</div>
            </div>
            
            <div class="nav-right">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar">AD</div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Super Admin</div>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-bell"></i> Notifications
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Content Container -->
        <div class="content-container">
            <!-- Messages Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Messages</div>
                        <div class="stat-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                    <div class="stat-value">127</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 8% from last week
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Pending Review</div>
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-value">23</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-down"></i> 12% from yesterday
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Accepted</div>
                        <div class="stat-icon">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="stat-value">89</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 15% from last month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Response Rate</div>
                        <div class="stat-icon">
                            <i class="fas fa-reply"></i>
                        </div>
                    </div>
                    <div class="stat-value">94%</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 3% from last week
                    </div>
                </div>
            </div>
            
            <!-- Messages Section -->
            <div class="data-section">
                <div class="section-header">
                    <div class="section-title">Client Messages</div>
                    <div class="section-actions">
                        <select class="action-btn btn-outline" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="accepted">Accepted</option>
                            <option value="declined">Declined</option>
                            <option value="replied">Replied</option>
                        </select>
                        <button class="action-btn btn-outline" id="bulkActionsBtn">
                            <i class="fas fa-tasks"></i> Bulk Actions
                        </button>
                        <button class="action-btn btn-outline">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="action-btn btn-primary">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" class="bulk-checkbox">
                                </th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Subject</th>
                                <th>Date</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td data-label="Select">
                                    <input type="checkbox" class="bulk-checkbox" data-id="1">
                                </td>
                                <td data-label="Name">John Smith</td>
                                <td data-label="Email"><EMAIL></td>
                                <td data-label="Subject">Website redesign inquiry</td>
                                <td data-label="Date">Today, 09:42</td>
                                <td data-label="Priority"><span class="status-badge status-urgent">High</span></td>
                                <td data-label="Status"><span class="status-badge status-pending">Pending</span></td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="1" title="View Message">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="accept" data-id="1" title="Accept">
                                            <i class="fas fa-check" style="color: var(--success);"></i>
                                        </button>
                                        <button class="table-btn" data-action="decline" data-id="1" title="Decline">
                                            <i class="fas fa-times" style="color: var(--error);"></i>
                                        </button>
                                        <button class="table-btn" data-action="reply" data-id="1" title="Reply">
                                            <i class="fas fa-reply"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="1" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td data-label="Select">
                                    <input type="checkbox" class="bulk-checkbox" data-id="2">
                                </td>
                                <td data-label="Name">Sarah Johnson</td>
                                <td data-label="Email"><EMAIL></td>
                                <td data-label="Subject">Partnership proposal</td>
                                <td data-label="Date">Today, 08:15</td>
                                <td data-label="Priority"><span class="status-badge status-pending">Medium</span></td>
                                <td data-label="Status"><span class="status-badge status-completed">Accepted</span></td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="2" title="View Message">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="accept" data-id="2" title="Accept">
                                            <i class="fas fa-check" style="color: var(--success);"></i>
                                        </button>
                                        <button class="table-btn" data-action="decline" data-id="2" title="Decline">
                                            <i class="fas fa-times" style="color: var(--error);"></i>
                                        </button>
                                        <button class="table-btn" data-action="reply" data-id="2" title="Reply">
                                            <i class="fas fa-reply"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="2" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- View Message Modal -->
    <div class="modal-backdrop" id="viewMessageModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-envelope-open"></i>
                    Message Details
                </h3>
                <button class="modal-close" data-modal="viewMessageModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="message-details">
                    <div class="message-header">
                        <div class="message-from">
                            <strong>From:</strong> <span id="messageFrom">John Smith</span>
                        </div>
                        <div class="message-email">
                            <strong>Email:</strong> <span id="messageEmail"><EMAIL></span>
                        </div>
                        <div class="message-date">
                            <strong>Date:</strong> <span id="messageDate">Today, 09:42</span>
                        </div>
                        <div class="message-subject">
                            <strong>Subject:</strong> <span id="messageSubject">Website redesign inquiry</span>
                        </div>
                    </div>
                    <div class="message-content">
                        <strong>Message:</strong>
                        <div id="messageContent" class="message-text">
                            Hello, I'm interested in redesigning my company website. We're looking for a modern, responsive design that reflects our brand values. Could you please provide more information about your services and pricing? We would also like to know about the timeline for such a project.
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-danger" id="declineMessageBtn">
                    <i class="fas fa-times"></i>
                    Decline
                </button>
                <button class="modal-btn modal-btn-secondary" id="replyMessageBtn">
                    <i class="fas fa-reply"></i>
                    Reply
                </button>
                <button class="modal-btn modal-btn-primary" id="acceptMessageBtn">
                    <i class="fas fa-check"></i>
                    Accept
                </button>
            </div>
        </div>
    </div>

    <!-- Reply Message Modal -->
    <div class="modal-backdrop" id="replyMessageModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-reply"></i>
                    Reply to Message
                </h3>
                <button class="modal-close" data-modal="replyMessageModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="replyMessageForm">
                    <div class="form-group">
                        <label class="form-label">To</label>
                        <input type="email" class="form-input" id="replyTo" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Subject</label>
                        <input type="text" class="form-input" id="replySubject" readonly>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Message</label>
                        <textarea class="form-input form-textarea" name="replyMessage" placeholder="Type your reply here..." required style="min-height: 150px;"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="markAsReplied" checked> Mark as replied after sending
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="replyMessageModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="sendReplyBtn">
                    <i class="fas fa-paper-plane"></i>
                    Send Reply
                </button>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div class="modal-backdrop" id="bulkActionsModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-tasks"></i>
                    Bulk Actions
                </h3>
                <button class="modal-close" data-modal="bulkActionsModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="bulk-actions-content">
                    <p>Selected <span id="selectedCount">0</span> messages</p>
                    <div class="bulk-action-buttons">
                        <button class="modal-btn modal-btn-primary" id="bulkAcceptBtn">
                            <i class="fas fa-check"></i>
                            Accept Selected
                        </button>
                        <button class="modal-btn modal-btn-secondary" id="bulkDeclineBtn">
                            <i class="fas fa-times"></i>
                            Decline Selected
                        </button>
                        <button class="modal-btn modal-btn-danger" id="bulkDeleteBtn">
                            <i class="fas fa-trash"></i>
                            Delete Selected
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="bulkActionsModal">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-backdrop" id="deleteModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Confirm Delete
                </h3>
                <button class="modal-close" data-modal="deleteModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation danger">
                    <div class="modal-icon">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                    <div class="modal-message">Are you sure you want to delete this message?</div>
                    <div class="modal-description">This action cannot be undone. The message will be permanently removed.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="deleteModal">Cancel</button>
                <button class="modal-btn modal-btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    Delete
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    <script src="./assets/js/index.js"></script>
    <script src="./assets/js/messages.js"></script>
</body>
</html>
