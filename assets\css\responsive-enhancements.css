/* ===================================
   RESPONSIVE DESIGN ENHANCEMENTS
   ===================================
   
   Additional responsive styles for modals,
   notifications, and enhanced mobile experience.
*/

/* ===== ENHANCED MOBILE NAVIGATION ===== */
@media (max-width: 1100px) {
    .sidebar {
        transform: translateX(-100%);
        box-shadow: none;
    }
    
    .sidebar.active {
        transform: translateX(0);
        box-shadow: var(--card-shadow-hover);
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .top-nav {
        left: 0;
        width: 100%;
    }
    
    .menu-toggle {
        display: block !important;
    }
    
    .sidebar-overlay.active {
        display: block;
        animation: fadeIn 0.3s ease;
    }
}

/* ===== TABLET RESPONSIVE ===== */
@media (max-width: 768px) {
    :root {
        --content-padding: 20px;
        --section-spacing: 30px;
    }
    
    .content-container {
        padding: var(--content-padding);
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-4);
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }
    
    .section-actions {
        width: 100%;
        flex-wrap: wrap;
        gap: var(--space-2);
    }
    
    .action-btn {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    .user-info {
        display: none;
    }
    
    .nav-right {
        gap: var(--space-3);
    }
    
    /* Enhanced table responsiveness */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .data-table {
        min-width: 600px;
    }
    
    .data-table th,
    .data-table td {
        padding: var(--space-3) var(--space-4);
        font-size: var(--font-sm);
    }
    
    /* Settings grid responsive */
    .settings-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .settings-card {
        padding: var(--space-4);
    }
}

/* ===== MOBILE RESPONSIVE ===== */
@media (max-width: 480px) {
    :root {
        --content-padding: 15px;
        --nav-height: 60px;
    }
    
    .top-nav {
        height: var(--nav-height);
        padding: 0 var(--space-4);
    }
    
    .page-title {
        font-size: var(--font-lg);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }
    
    .stat-card {
        padding: var(--space-4);
    }
    
    .stat-value {
        font-size: var(--font-3xl);
    }
    
    .section-actions {
        flex-direction: column;
    }
    
    .action-btn {
        width: 100%;
        padding: var(--space-3) var(--space-4);
    }
    
    /* Mobile table cards */
    .data-table,
    .data-table thead,
    .data-table tbody,
    .data-table th,
    .data-table td,
    .data-table tr {
        display: block;
    }
    
    .data-table thead tr {
        position: absolute;
        top: -9999px;
        left: -9999px;
    }
    
    .data-table tr {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        margin-bottom: var(--space-3);
        padding: var(--space-4);
    }
    
    .data-table td {
        border: none;
        position: relative;
        padding: var(--space-2) 0;
        padding-left: 40%;
        text-align: right;
    }
    
    .data-table td:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        width: 35%;
        padding-right: var(--space-2);
        white-space: nowrap;
        font-weight: var(--font-medium);
        color: var(--text-secondary);
        text-align: left;
    }
    
    .action-btns {
        justify-content: flex-end;
        margin-top: var(--space-2);
    }
}

/* ===== MODAL RESPONSIVE ENHANCEMENTS ===== */
@media (max-width: 768px) {
    .modal-backdrop {
        padding: var(--space-2);
        align-items: flex-start;
        padding-top: var(--space-8);
    }
    
    .modal {
        max-width: 100%;
        width: 100%;
        max-height: 85vh;
        margin: 0;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: var(--space-4);
        padding-right: var(--space-4);
    }
    
    .modal-body {
        max-height: 50vh;
        padding-top: var(--space-4);
        padding-bottom: var(--space-4);
    }
    
    .modal-footer {
        flex-direction: column;
        gap: var(--space-2);
    }
    
    .modal-btn {
        width: 100%;
        order: 2;
    }
    
    .modal-btn-primary {
        order: 1;
    }
    
    .modal-title {
        font-size: var(--font-lg);
    }
    
    .form-group {
        margin-bottom: var(--space-4);
    }
    
    .form-input,
    .form-textarea {
        font-size: var(--font-base);
        padding: var(--space-3);
    }
}

@media (max-width: 480px) {
    .modal-backdrop {
        padding: var(--space-1);
        padding-top: var(--space-4);
    }
    
    .modal {
        max-height: 90vh;
    }
    
    .modal-header {
        padding: var(--space-4) var(--space-3);
    }
    
    .modal-body {
        padding: var(--space-3);
        max-height: 60vh;
    }
    
    .modal-footer {
        padding: var(--space-3);
    }
    
    .modal-confirmation .modal-icon {
        font-size: var(--font-3xl);
    }
}

/* ===== NOTIFICATION RESPONSIVE ENHANCEMENTS ===== */
@media (max-width: 768px) {
    .notification-container {
        top: var(--space-2);
        right: var(--space-2);
        left: var(--space-2);
        max-width: none;
    }
    
    .notification {
        transform: translateY(-100%);
        margin-bottom: var(--space-2);
    }
    
    .notification.show {
        transform: translateY(0);
    }
    
    .notification.hide {
        transform: translateY(-100%);
    }
    
    .notification.large {
        max-width: none;
        padding: var(--space-4);
    }
}

@media (max-width: 480px) {
    .notification-container {
        top: calc(var(--nav-height) + var(--space-2));
        right: var(--space-1);
        left: var(--space-1);
    }
    
    .notification {
        padding: var(--space-3);
    }
    
    .notification-title {
        font-size: var(--font-sm);
    }
    
    .notification-message {
        font-size: var(--font-xs);
    }
    
    .notification-actions {
        flex-direction: column;
        gap: var(--space-2);
        margin-top: var(--space-3);
    }
    
    .notification-action {
        justify-content: center;
        width: 100%;
    }
    
    .notification.compact .notification-actions {
        display: none;
    }
}

/* ===== TOUCH ENHANCEMENTS ===== */
@media (hover: none) and (pointer: coarse) {
    .table-btn,
    .action-btn,
    .modal-btn,
    .notification-close,
    .modal-close {
        min-height: 44px;
        min-width: 44px;
    }
    
    .menu-item {
        padding: var(--space-4) var(--space-6);
    }
    
    .toggle-switch {
        width: 60px;
        height: 32px;
    }
    
    .toggle-switch::after {
        width: 28px;
        height: 28px;
        top: 2px;
        left: 2px;
    }
    
    .toggle-switch.active::after {
        transform: translateX(28px);
    }
}

/* ===== LANDSCAPE MOBILE ===== */
@media (max-width: 768px) and (orientation: landscape) {
    .modal-backdrop {
        align-items: center;
        padding-top: var(--space-4);
    }
    
    .modal {
        max-height: 80vh;
    }
    
    .modal-body {
        max-height: 40vh;
    }
    
    .notification-container {
        top: var(--space-2);
        max-width: 300px;
        right: var(--space-2);
        left: auto;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .modal,
    .notification {
        transform: none !important;
    }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
    :root {
        --border-color: currentColor;
        --card-shadow: 0 0 0 2px currentColor;
        --button-shadow: 0 0 0 2px currentColor;
    }
    
    .modal,
    .notification,
    .stat-card,
    .data-table {
        border: 2px solid currentColor;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .sidebar,
    .top-nav,
    .modal-backdrop,
    .notification-container,
    .action-btn,
    .table-btn {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        padding-top: 0;
    }
    
    .content-container {
        padding: 0;
    }
    
    .data-table {
        border-collapse: collapse;
    }
    
    .data-table th,
    .data-table td {
        border: 1px solid #000;
        padding: 8px;
    }
}
