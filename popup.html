<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup Demo - DigitalAce</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
    <style>
        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-family-primary);
            margin: 0;
            padding: var(--space-4);
        }

        .demo-container {
            text-align: center;
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: var(--space-16);
            box-shadow: var(--card-shadow-hover);
            max-width: 400px;
            width: 100%;
        }

        .demo-title {
            font-size: var(--font-3xl);
            font-weight: var(--font-bold);
            color: var(--text-color);
            margin-bottom: var(--space-4);
            text-shadow: var(--text-shadow);
        }

        .demo-description {
            color: var(--text-secondary);
            margin-bottom: var(--space-8);
            line-height: var(--leading-relaxed);
        }

        .click-button {
            background: var(--primary);
            color: white;
            border: none;
            padding: var(--space-4) var(--space-8);
            border-radius: var(--radius-lg);
            font-size: var(--font-lg);
            font-weight: var(--font-semibold);
            cursor: pointer;
            transition: var(--transition);
            box-shadow: var(--button-shadow);
            display: inline-flex;
            align-items: center;
            gap: var(--space-3);
        }

        .click-button:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: var(--button-shadow-hover);
        }

        .click-button:active {
            transform: translateY(-1px);
        }

        .theme-toggle {
            position: absolute;
            top: var(--space-4);
            right: var(--space-4);
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: var(--font-lg);
        }

        .theme-toggle:hover {
            background: var(--hover-bg);
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <button class="theme-toggle" id="themeToggle">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Main Demo Container -->
    <div class="demo-container">
        <h1 class="demo-title">
            <i class="fas fa-rocket"></i>
            Popup Demo
        </h1>
        <p class="demo-description">
            Click the button below to see a beautiful popup form with cross button cancellation and submit functionality.
        </p>
        <button class="click-button" id="openPopupBtn">
            <i class="fas fa-external-link-alt"></i>
            Click Me
        </button>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Demo Form Modal -->
    <div class="modal-backdrop" id="demoModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-edit"></i>
                    Demo Form
                </h3>
                <button class="modal-close" data-modal="demoModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="demoForm" method="post" action="popup.php">
                    <div class="form-group">
                        <label class="form-label">Full Name</label>
                        <input name="fullName" type="text" class="form-input" name="fullName" placeholder="Enter your full name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Email Address</label>
                        <input name="email" type="email" class="form-input" name="email" placeholder="Enter your email" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Phone Number</label>
                        <input name="phone" type="tel" class="form-input" name="phone" placeholder="Enter your phone number">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Subject</label>
                        <select  class="form-input form-select" name="subject" required>
                            <option value="">Select a subject</option>
                            <option value="general">General Inquiry</option>
                            <option value="support">Technical Support</option>
                            <option value="billing">Billing Question</option>
                            <option value="feedback">Feedback</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Message</label>
                        <textarea name="message" class="form-input form-textarea" name="message" placeholder="Enter your message here..." required style="min-height: 100px;"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label" style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" name="newsletter" style="margin: 0;">
                            Subscribe to our newsletter
                        </label>
                    </div>
                    <button name="submit" type="submit" class="modal-btn modal-btn-primary" id="submitFormBtn">
                    <i class="fas fa-paper-plane"></i>
                    Submit
                </button>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="demoModal">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <!-- <button class="modal-btn modal-btn-primary" id="submitFormBtn">
                    <i class="fas fa-paper-plane"></i>
                    Submit
                </button> -->
            </div>
        </div>
    </div>

    <!-- Include JavaScript Files -->
    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    
    <script>
        // Initialize the popup demo functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Theme toggle functionality
            const themeToggle = document.getElementById('themeToggle');
            const currentTheme = localStorage.getItem('theme') || 'dark';
            
            // Set initial theme
            document.documentElement.setAttribute('data-theme', currentTheme);
            updateThemeIcon(currentTheme);
            
            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            });
            
            function updateThemeIcon(theme) {
                const icon = themeToggle.querySelector('i');
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            // Open popup button
            const openPopupBtn = document.getElementById('openPopupBtn');
            openPopupBtn.addEventListener('click', () => {
                window.modalSystem.openModal('demoModal');
            });

            // // Submit form button
            // const submitFormBtn = document.getElementById('submitFormBtn');
            // submitFormBtn.addEventListener('click', () => {
            //     const form = document.getElementById('demoForm');
                
            //     // Basic form validation
            //     if (!form.checkValidity()) {
            //         form.reportValidity();
            //         return;
            //     }

            //     // Get form data
            //     const formData = new FormData(form);
            //     const data = Object.fromEntries(formData.entries());
                
            //     // Simulate form submission
            //     setTimeout(() => {
            //         // Close modal
            //         window.modalSystem.closeModal('demoModal');
                    
            //         // Show success notification
            //         window.notificationSystem.success(
            //             'Form Submitted!', 
            //             `Thank you ${data.fullName}! Your form has been submitted successfully.`,
            //             {
            //                 actions: [
            //                     { text: 'View Details', type: 'primary', action: 'view' },
            //                     { text: 'Dismiss', type: 'secondary', action: 'dismiss' }
            //                 ]
            //             }
            //         );
                    
            //         // Reset form
            //         form.reset();
                    
            //         // Log form data (in real app, send to server)
            //         console.log('Form submitted with data:', data);
            //     }, 500);
            // });

            // Initialize notification system
            window.notificationSystem = new NotificationSystem();
            
            // Show welcome notification
            setTimeout(() => {
                window.notificationSystem.info(
                    'Welcome!', 
                    'Click the "Click Me" button to see the popup form in action.',
                    { duration: 4000 }
                );
            }, 1000);
        });
    </script>
</body>
</html>
