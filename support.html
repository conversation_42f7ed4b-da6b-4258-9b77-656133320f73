<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Queries - DigitalAce Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                DigitalAce
            </a>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-title">Main</div>
            <a href="index.html" class="menu-item">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            
            <div class="menu-title">Management</div>
            <a href="projects.html" class="menu-item">
                <i class="fas fa-project-diagram"></i>
                Projects
            </a>
            <a href="messages.html" class="menu-item">
                <i class="fas fa-comments"></i>
                Client Messages
            </a>
            <a href="support.html" class="menu-item active">
                <i class="fas fa-question-circle"></i>
                Support Queries
            </a>
            <a href="users.html" class="menu-item">
                <i class="fas fa-users"></i>
                Users
            </a>
            <a href="hosting.html" class="menu-item">
                <i class="fas fa-server"></i>
                Hosting Services
            </a>
            <a href="gps-tracker.html" class="menu-item">
                <i class="fas fa-map-marker-alt"></i>
                GPS Trackers
            </a>
            
            <div class="menu-title">Settings</div>
            <a href="settings.html" class="menu-item">
                <i class="fas fa-cog"></i>
                System Settings
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-user-shield"></i>
                Admin Accounts
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <div class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="page-title">Support Queries</div>
            </div>
            
            <div class="nav-right">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar">AD</div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Super Admin</div>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-bell"></i> Notifications
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Content Container -->
        <div class="content-container">
            <!-- Support Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Tickets</div>
                        <div class="stat-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                    </div>
                    <div class="stat-value">156</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 12% from last week
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Open Tickets</div>
                        <div class="stat-icon">
                            <i class="fas fa-folder-open"></i>
                        </div>
                    </div>
                    <div class="stat-value">34</div>
                    <div class="stat-trend down">
                        <i class="fas fa-arrow-down"></i> 8% from yesterday
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Resolved Today</div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">18</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 25% from yesterday
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Avg Response Time</div>
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-value">2.4h</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-down"></i> 15% improvement
                    </div>
                </div>
            </div>
            
            <!-- Support Tickets Section -->
            <div class="data-section">
                <div class="section-header">
                    <div class="section-title">Support Tickets</div>
                    <div class="section-actions">
                        <select class="action-btn btn-outline" id="priorityFilter">
                            <option value="">All Priorities</option>
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                            <option value="critical">Critical</option>
                        </select>
                        <select class="action-btn btn-outline" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="open">Open</option>
                            <option value="in-progress">In Progress</option>
                            <option value="resolved">Resolved</option>
                            <option value="closed">Closed</option>
                        </select>
                        <button class="action-btn btn-outline">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="action-btn btn-primary" id="newTicketBtn">
                            <i class="fas fa-plus"></i> New Ticket
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Ticket ID</th>
                                <th>Customer</th>
                                <th>Issue Type</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Assigned To</th>
                                <th>Created</th>
                                <th>Last Update</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td data-label="Ticket ID">#TKT-00125</td>
                                <td data-label="Customer">James Wilson</td>
                                <td data-label="Issue Type">Hosting Issue</td>
                                <td data-label="Priority"><span class="status-badge status-urgent">High</span></td>
                                <td data-label="Status"><span class="status-badge status-pending">Open</span></td>
                                <td data-label="Assigned To">John Doe</td>
                                <td data-label="Created">Today, 10:15</td>
                                <td data-label="Last Update">2 hours ago</td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="125" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="assign" data-id="125" title="Assign">
                                            <i class="fas fa-user-plus"></i>
                                        </button>
                                        <button class="table-btn" data-action="resolve" data-id="125" title="Mark Resolved">
                                            <i class="fas fa-check" style="color: var(--success);"></i>
                                        </button>
                                        <button class="table-btn" data-action="escalate" data-id="125" title="Escalate">
                                            <i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="125" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td data-label="Ticket ID">#TKT-00124</td>
                                <td data-label="Customer">Lisa Anderson</td>
                                <td data-label="Issue Type">GPS Device</td>
                                <td data-label="Priority"><span class="status-badge status-pending">Medium</span></td>
                                <td data-label="Status"><span class="status-badge status-warning">In Progress</span></td>
                                <td data-label="Assigned To">Jane Smith</td>
                                <td data-label="Created">Today, 08:45</td>
                                <td data-label="Last Update">1 hour ago</td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="124" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="assign" data-id="124" title="Assign">
                                            <i class="fas fa-user-plus"></i>
                                        </button>
                                        <button class="table-btn" data-action="resolve" data-id="124" title="Mark Resolved">
                                            <i class="fas fa-check" style="color: var(--success);"></i>
                                        </button>
                                        <button class="table-btn" data-action="escalate" data-id="124" title="Escalate">
                                            <i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="124" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- View Ticket Modal -->
    <div class="modal-backdrop" id="viewTicketModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-ticket-alt"></i>
                    Ticket Details - <span id="ticketId">#TKT-00125</span>
                </h3>
                <button class="modal-close" data-modal="viewTicketModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="ticket-details">
                    <div class="ticket-header">
                        <div class="ticket-info-grid">
                            <div class="ticket-info-item">
                                <strong>Customer:</strong> <span id="ticketCustomer">James Wilson</span>
                            </div>
                            <div class="ticket-info-item">
                                <strong>Email:</strong> <span id="ticketEmail"><EMAIL></span>
                            </div>
                            <div class="ticket-info-item">
                                <strong>Issue Type:</strong> <span id="ticketType">Hosting Issue</span>
                            </div>
                            <div class="ticket-info-item">
                                <strong>Priority:</strong> <span id="ticketPriority" class="status-badge status-urgent">High</span>
                            </div>
                            <div class="ticket-info-item">
                                <strong>Status:</strong> <span id="ticketStatus" class="status-badge status-pending">Open</span>
                            </div>
                            <div class="ticket-info-item">
                                <strong>Assigned To:</strong> <span id="ticketAssigned">John Doe</span>
                            </div>
                            <div class="ticket-info-item">
                                <strong>Created:</strong> <span id="ticketCreated">Today, 10:15</span>
                            </div>
                            <div class="ticket-info-item">
                                <strong>Last Update:</strong> <span id="ticketUpdated">2 hours ago</span>
                            </div>
                        </div>
                    </div>
                    <div class="ticket-description">
                        <strong>Description:</strong>
                        <div id="ticketDescription" class="ticket-text">
                            Our website has been experiencing intermittent downtime for the past 24 hours. Users are reporting 503 errors when trying to access the site. This is affecting our business operations significantly. Please investigate and resolve as soon as possible.
                        </div>
                    </div>
                    <div class="ticket-history">
                        <strong>Ticket History:</strong>
                        <div class="history-timeline">
                            <div class="history-item">
                                <div class="history-time">2 hours ago</div>
                                <div class="history-action">Ticket assigned to John Doe</div>
                            </div>
                            <div class="history-item">
                                <div class="history-time">Today, 10:15</div>
                                <div class="history-action">Ticket created by James Wilson</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" id="escalateTicketBtn">
                    <i class="fas fa-exclamation-triangle"></i>
                    Escalate
                </button>
                <button class="modal-btn modal-btn-secondary" id="assignTicketBtn">
                    <i class="fas fa-user-plus"></i>
                    Reassign
                </button>
                <button class="modal-btn modal-btn-primary" id="resolveTicketBtn">
                    <i class="fas fa-check"></i>
                    Mark Resolved
                </button>
            </div>
        </div>
    </div>

    <!-- New Ticket Modal -->
    <div class="modal-backdrop" id="newTicketModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus"></i>
                    Create Support Ticket
                </h3>
                <button class="modal-close" data-modal="newTicketModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="newTicketForm">
                    <div class="form-group">
                        <label class="form-label">Customer Name</label>
                        <input type="text" class="form-input" name="customerName" placeholder="Enter customer name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Customer Email</label>
                        <input type="email" class="form-input" name="customerEmail" placeholder="Enter customer email" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Issue Type</label>
                        <select class="form-input form-select" name="issueType" required>
                            <option value="">Select issue type</option>
                            <option value="hosting">Hosting Issue</option>
                            <option value="gps">GPS Device</option>
                            <option value="billing">Billing Inquiry</option>
                            <option value="website">Website Error</option>
                            <option value="feature">Feature Request</option>
                            <option value="technical">Technical Support</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Priority</label>
                        <select class="form-input form-select" name="priority" required>
                            <option value="">Select priority</option>
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Assign To</label>
                        <select class="form-input form-select" name="assignedTo">
                            <option value="">Unassigned</option>
                            <option value="john-doe">John Doe</option>
                            <option value="jane-smith">Jane Smith</option>
                            <option value="mike-johnson">Mike Johnson</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea class="form-input form-textarea" name="description" placeholder="Describe the issue in detail" required style="min-height: 120px;"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="newTicketModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="saveTicketBtn">
                    <i class="fas fa-save"></i>
                    Create Ticket
                </button>
            </div>
        </div>
    </div>

    <!-- Assign Ticket Modal -->
    <div class="modal-backdrop" id="assignTicketModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    Assign Ticket
                </h3>
                <button class="modal-close" data-modal="assignTicketModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="assignTicketForm">
                    <div class="form-group">
                        <label class="form-label">Assign To</label>
                        <select class="form-input form-select" name="assignTo" required>
                            <option value="">Select team member</option>
                            <option value="john-doe">John Doe - Senior Developer</option>
                            <option value="jane-smith">Jane Smith - Support Specialist</option>
                            <option value="mike-johnson">Mike Johnson - System Admin</option>
                            <option value="sarah-wilson">Sarah Wilson - Project Manager</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Assignment Note</label>
                        <textarea class="form-input form-textarea" name="assignmentNote" placeholder="Add a note about this assignment (optional)"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="assignTicketModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="confirmAssignBtn">
                    <i class="fas fa-user-plus"></i>
                    Assign Ticket
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-backdrop" id="deleteModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Confirm Delete
                </h3>
                <button class="modal-close" data-modal="deleteModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation danger">
                    <div class="modal-icon">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                    <div class="modal-message">Are you sure you want to delete this ticket?</div>
                    <div class="modal-description">This action cannot be undone. All ticket data and history will be permanently removed.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="deleteModal">Cancel</button>
                <button class="modal-btn modal-btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    Delete
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    <script src="./assets/js/index.js"></script>
    <script src="./assets/js/support.js"></script>
</body>
</html>
