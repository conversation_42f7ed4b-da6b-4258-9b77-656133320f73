<?php

$category_id = '';
$product_id = '';
$tdi = '';

include('./connect.php');

if(isset($_Get['category'])){
    $category_id = $_GET['category'];
    $category_id = htmlspecialchars($category_id);
    
    if(isset($_GET['pro-id'])){
        $product_id = $_GET['pro-id'];
        $product_id = htmlspecialchars($product_id);

        if(isset($_GET['tdi'])){
            $tdi = $_GET['tdi'];
            $tdi = htmlspecialchars($tdi);

            // Your code here
        }else{
            echo "TDI not found";
        }
    }else{
        echo "Product not found";
    }
}else{
    echo "Category not found";
}


if($category_id && $category_id == 'hosting'){
    $sql = "SELECT * FROM products WHERE category = 'hosting' AND id = '$product_id'";
    $result = mysqli_query($conn, $sql);
    $row_hosting = mysqli_fetch_assoc($result);

}
?>