<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users Management - DigitalAce Admin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
</head>
<body>
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                DigitalAce
            </a>
        </div>
        
        <div class="sidebar-menu">
            <div class="menu-title">Main</div>
            <a href="index.html" class="menu-item">
                <i class="fas fa-tachometer-alt"></i>
                Dashboard
            </a>
            
            <div class="menu-title">Management</div>
            <a href="projects.html" class="menu-item">
                <i class="fas fa-project-diagram"></i>
                Projects
            </a>
            <a href="messages.html" class="menu-item">
                <i class="fas fa-comments"></i>
                Client Messages
            </a>
            <a href="support.html" class="menu-item">
                <i class="fas fa-question-circle"></i>
                Support Queries
            </a>
            <a href="users.html" class="menu-item active">
                <i class="fas fa-users"></i>
                Users
            </a>
            <a href="hosting.html" class="menu-item">
                <i class="fas fa-server"></i>
                Hosting Services
            </a>
            <a href="gps-tracker.html" class="menu-item">
                <i class="fas fa-map-marker-alt"></i>
                GPS Trackers
            </a>
            
            <div class="menu-title">Settings</div>
            <a href="settings.html" class="menu-item">
                <i class="fas fa-cog"></i>
                System Settings
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-user-shield"></i>
                Admin Accounts
            </a>
            <a href="#" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                Logout
            </a>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Navigation -->
        <nav class="top-nav">
            <div class="nav-left">
                <div class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="page-title">Users Management</div>
            </div>
            
            <div class="nav-right">
                <button class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </button>
                
                <div class="user-profile" id="userProfile">
                    <div class="user-avatar">AD</div>
                    <div class="user-info">
                        <div class="user-name">Admin User</div>
                        <div class="user-role">Super Admin</div>
                    </div>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-user"></i> My Profile
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-cog"></i> Account Settings
                        </a>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-bell"></i> Notifications
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Content Container -->
        <div class="content-container">
            <!-- Users Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Users</div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-value">1,248</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 22% from last month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Active Users</div>
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                    <div class="stat-value">1,156</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 18% from last week
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Banned Users</div>
                        <div class="stat-icon">
                            <i class="fas fa-user-slash"></i>
                        </div>
                    </div>
                    <div class="stat-value">92</div>
                    <div class="stat-trend down">
                        <i class="fas fa-arrow-down"></i> 5% from last month
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">New This Week</div>
                        <div class="stat-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="stat-value">47</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i> 12% from last week
                    </div>
                </div>
            </div>
            
            <!-- Users Section -->
            <div class="data-section">
                <div class="section-header">
                    <div class="section-title">All Users</div>
                    <div class="section-actions">
                        <select class="action-btn btn-outline" id="roleFilter">
                            <option value="">All Roles</option>
                            <option value="admin">Admin</option>
                            <option value="user">User</option>
                            <option value="moderator">Moderator</option>
                            <option value="client">Client</option>
                        </select>
                        <select class="action-btn btn-outline" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="banned">Banned</option>
                            <option value="pending">Pending</option>
                        </select>
                        <button class="action-btn btn-outline">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <button class="action-btn btn-primary" id="addUserBtn">
                            <i class="fas fa-user-plus"></i> Add User
                        </button>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Joined</th>
                                <th>Projects</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td data-label="User">
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div class="user-avatar" style="width: 32px; height: 32px; font-size: 12px;">JD</div>
                                        <div>
                                            <div style="font-weight: 600;">John Doe</div>
                                            <div style="font-size: 12px; color: var(--text-secondary);">ID: #USR-001</div>
                                        </div>
                                    </div>
                                </td>
                                <td data-label="Email"><EMAIL></td>
                                <td data-label="Role"><span class="status-badge status-completed">Admin</span></td>
                                <td data-label="Status"><span class="status-badge status-completed">Active</span></td>
                                <td data-label="Last Login">2 hours ago</td>
                                <td data-label="Joined">Jan 15, 2023</td>
                                <td data-label="Projects">12</td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="1" title="View Profile">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="edit" data-id="1" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-btn" data-action="ban" data-id="1" title="Ban User" style="color: var(--warning);">
                                            <i class="fas fa-user-slash"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="1" title="Delete User">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td data-label="User">
                                    <div style="display: flex; align-items: center; gap: 12px;">
                                        <div class="user-avatar" style="width: 32px; height: 32px; font-size: 12px; background: var(--secondary);">JS</div>
                                        <div>
                                            <div style="font-weight: 600;">Jane Smith</div>
                                            <div style="font-size: 12px; color: var(--text-secondary);">ID: #USR-002</div>
                                        </div>
                                    </div>
                                </td>
                                <td data-label="Email"><EMAIL></td>
                                <td data-label="Role"><span class="status-badge status-pending">User</span></td>
                                <td data-label="Status"><span class="status-badge status-completed">Active</span></td>
                                <td data-label="Last Login">1 day ago</td>
                                <td data-label="Joined">Feb 20, 2023</td>
                                <td data-label="Projects">8</td>
                                <td data-label="Actions">
                                    <div class="action-btns">
                                        <button class="table-btn" data-action="view" data-id="2" title="View Profile">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="table-btn" data-action="edit" data-id="2" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-btn" data-action="ban" data-id="2" title="Ban User" style="color: var(--warning);">
                                            <i class="fas fa-user-slash"></i>
                                        </button>
                                        <button class="table-btn delete" data-action="delete" data-id="2" title="Delete User">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- View User Modal -->
    <div class="modal-backdrop" id="viewUserModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user"></i>
                    User Profile - <span id="userName">John Doe</span>
                </h3>
                <button class="modal-close" data-modal="viewUserModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="user-profile-header" style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
                    <div class="user-avatar-large" id="userAvatarLarge">JD</div>
                    <div>
                        <h4 id="userFullName">John Doe</h4>
                        <p style="color: var(--text-secondary); margin: 0;">ID: <span id="userId">#USR-001</span></p>
                        <p style="color: var(--text-secondary); margin: 5px 0 0 0;">Member since <span id="userJoinDate">Jan 15, 2023</span></p>
                    </div>
                </div>

                <div class="user-details-grid">
                    <div class="user-detail-card">
                        <div class="user-detail-title">
                            <i class="fas fa-envelope"></i>
                            Contact Information
                        </div>
                        <div class="user-detail-content">
                            <p><strong>Email:</strong> <span id="userEmail"><EMAIL></span></p>
                            <p><strong>Phone:</strong> <span id="userPhone">+****************</span></p>
                            <p><strong>Location:</strong> <span id="userLocation">New York, USA</span></p>
                        </div>
                    </div>

                    <div class="user-detail-card">
                        <div class="user-detail-title">
                            <i class="fas fa-user-tag"></i>
                            Account Details
                        </div>
                        <div class="user-detail-content">
                            <p><strong>Role:</strong> <span id="userRole" class="status-badge status-completed">Admin</span></p>
                            <p><strong>Status:</strong> <span id="userStatus" class="status-badge status-completed">Active</span></p>
                            <p><strong>Last Login:</strong> <span id="userLastLogin">2 hours ago</span></p>
                        </div>
                    </div>

                    <div class="user-detail-card">
                        <div class="user-detail-title">
                            <i class="fas fa-chart-bar"></i>
                            Activity Stats
                        </div>
                        <div class="user-detail-content">
                            <p><strong>Projects:</strong> <span id="userProjects">12</span></p>
                            <p><strong>Messages:</strong> <span id="userMessages">45</span></p>
                            <p><strong>Support Tickets:</strong> <span id="userTickets">3</span></p>
                        </div>
                    </div>

                    <div class="user-detail-card">
                        <div class="user-detail-title">
                            <i class="fas fa-cog"></i>
                            Permissions
                        </div>
                        <div class="user-detail-content">
                            <p><strong>Can Create Projects:</strong> <span id="userCanCreate">Yes</span></p>
                            <p><strong>Can Manage Users:</strong> <span id="userCanManage">Yes</span></p>
                            <p><strong>Can Access Reports:</strong> <span id="userCanReports">Yes</span></p>
                        </div>
                    </div>
                </div>

                <div class="user-actions-grid">
                    <button class="user-action-btn edit" id="editUserFromProfile">
                        <i class="fas fa-edit"></i>
                        Edit Profile
                    </button>
                    <button class="user-action-btn ban" id="banUserFromProfile">
                        <i class="fas fa-user-slash"></i>
                        Ban User
                    </button>
                    <button class="user-action-btn delete" id="deleteUserFromProfile">
                        <i class="fas fa-trash"></i>
                        Delete User
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="viewUserModal">Close</button>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal-backdrop" id="editUserModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-edit"></i>
                    Edit User
                </h3>
                <button class="modal-close" data-modal="editUserModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="editUserForm">
                    <div class="form-group">
                        <label class="form-label">Full Name</label>
                        <input type="text" class="form-input" name="fullName" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-input" name="email" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Phone</label>
                        <input type="tel" class="form-input" name="phone">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Location</label>
                        <input type="text" class="form-input" name="location">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Role</label>
                        <select class="form-input form-select" name="role" required>
                            <option value="user">User</option>
                            <option value="moderator">Moderator</option>
                            <option value="admin">Admin</option>
                            <option value="client">Client</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select class="form-input form-select" name="status" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="banned">Banned</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Permissions</label>
                        <div style="display: flex; flex-direction: column; gap: 10px; margin-top: 10px;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="canCreate" checked>
                                Can Create Projects
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="canManage">
                                Can Manage Users
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="canReports" checked>
                                Can Access Reports
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="editUserModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="saveUserBtn">
                    <i class="fas fa-save"></i>
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal-backdrop" id="addUserModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    Add New User
                </h3>
                <button class="modal-close" data-modal="addUserModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="modal-form" id="addUserForm">
                    <div class="form-group">
                        <label class="form-label">Full Name</label>
                        <input type="text" class="form-input" name="fullName" placeholder="Enter full name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-input" name="email" placeholder="Enter email address" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-input" name="password" placeholder="Enter password" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Phone</label>
                        <input type="tel" class="form-input" name="phone" placeholder="Enter phone number">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Location</label>
                        <input type="text" class="form-input" name="location" placeholder="Enter location">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Role</label>
                        <select class="form-input form-select" name="role" required>
                            <option value="">Select role</option>
                            <option value="user">User</option>
                            <option value="moderator">Moderator</option>
                            <option value="admin">Admin</option>
                            <option value="client">Client</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" name="sendWelcomeEmail" checked> Send welcome email
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="addUserModal">Cancel</button>
                <button class="modal-btn modal-btn-primary" id="createUserBtn">
                    <i class="fas fa-user-plus"></i>
                    Create User
                </button>
            </div>
        </div>
    </div>

    <!-- Ban User Confirmation Modal -->
    <div class="modal-backdrop" id="banUserModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-slash"></i>
                    Ban User
                </h3>
                <button class="modal-close" data-modal="banUserModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation danger">
                    <div class="modal-icon">
                        <i class="fas fa-user-slash"></i>
                    </div>
                    <div class="modal-message">Are you sure you want to ban this user?</div>
                    <div class="modal-description">The user will be unable to access their account and all active sessions will be terminated.</div>
                </div>
                <div class="form-group" style="margin-top: 20px;">
                    <label class="form-label">Reason for ban (optional)</label>
                    <textarea class="form-input form-textarea" id="banReason" placeholder="Enter reason for banning this user..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="banUserModal">Cancel</button>
                <button class="modal-btn modal-btn-danger" id="confirmBanBtn">
                    <i class="fas fa-user-slash"></i>
                    Ban User
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-backdrop" id="deleteModal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Confirm Delete
                </h3>
                <button class="modal-close" data-modal="deleteModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation danger">
                    <div class="modal-icon">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                    <div class="modal-message">Are you sure you want to delete this user?</div>
                    <div class="modal-description">This action cannot be undone. All user data, projects, and history will be permanently removed.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn modal-btn-secondary" data-modal="deleteModal">Cancel</button>
                <button class="modal-btn modal-btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    Delete User
                </button>
            </div>
        </div>
    </div>

    <script src="./assets/js/modal-system.js"></script>
    <script src="./assets/js/notification-system.js"></script>
    <script src="./assets/js/index.js"></script>
    <script src="./assets/js/users.js"></script>
</body>
</html>
