/* ===================================
   SUPPORT TICKETS PAGE FUNCTIONALITY
   ===================================
   
   Handles ticket management, status updates,
   assignments, and ticket interactions.
*/

class SupportManager {
    constructor() {
        this.currentTicket = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeFilters();
    }

    bindEvents() {
        // Ticket action buttons
        document.addEventListener('click', (e) => {
            const action = e.target.closest('[data-action]')?.getAttribute('data-action');
            const ticketId = e.target.closest('[data-id]')?.getAttribute('data-id');

            if (action && ticketId) {
                this.handleTicketAction(action, ticketId);
            }
        });

        // New ticket button
        const newTicketBtn = document.getElementById('newTicketBtn');
        if (newTicketBtn) {
            newTicketBtn.addEventListener('click', () => {
                window.modalSystem.openModal('newTicketModal');
            });
        }

        // Save ticket button
        const saveTicketBtn = document.getElementById('saveTicketBtn');
        if (saveTicketBtn) {
            saveTicketBtn.addEventListener('click', () => {
                this.createTicket();
            });
        }

        // Filters
        const priorityFilter = document.getElementById('priorityFilter');
        const statusFilter = document.getElementById('statusFilter');

        if (priorityFilter) {
            priorityFilter.addEventListener('change', (e) => {
                this.filterTickets('priority', e.target.value);
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterTickets('status', e.target.value);
            });
        }

        // Modal action buttons
        this.bindModalActions();
    }

    bindModalActions() {
        // Resolve ticket
        const resolveBtn = document.getElementById('resolveTicketBtn');
        if (resolveBtn) {
            resolveBtn.addEventListener('click', () => {
                this.resolveTicket(this.currentTicket);
            });
        }

        // Escalate ticket
        const escalateBtn = document.getElementById('escalateTicketBtn');
        if (escalateBtn) {
            escalateBtn.addEventListener('click', () => {
                this.escalateTicket(this.currentTicket);
            });
        }

        // Assign ticket
        const assignBtn = document.getElementById('assignTicketBtn');
        if (assignBtn) {
            assignBtn.addEventListener('click', () => {
                this.openAssignModal();
            });
        }

        // Confirm assignment
        const confirmAssignBtn = document.getElementById('confirmAssignBtn');
        if (confirmAssignBtn) {
            confirmAssignBtn.addEventListener('click', () => {
                this.assignTicket();
            });
        }
    }

    handleTicketAction(action, ticketId) {
        this.currentTicket = ticketId;

        switch (action) {
            case 'view':
                this.viewTicket(ticketId);
                break;
            case 'assign':
                this.openAssignModal(ticketId);
                break;
            case 'resolve':
                this.resolveTicket(ticketId);
                break;
            case 'escalate':
                this.escalateTicket(ticketId);
                break;
            case 'delete':
                this.deleteTicket(ticketId);
                break;
        }
    }

    viewTicket(ticketId) {
        const ticketData = this.getTicketData(ticketId);
        
        // Populate modal with ticket data
        document.getElementById('ticketId').textContent = `#TKT-${ticketId.padStart(5, '0')}`;
        document.getElementById('ticketCustomer').textContent = ticketData.customer;
        document.getElementById('ticketEmail').textContent = ticketData.email;
        document.getElementById('ticketType').textContent = ticketData.type;
        document.getElementById('ticketCreated').textContent = ticketData.created;
        document.getElementById('ticketUpdated').textContent = ticketData.updated;
        document.getElementById('ticketDescription').textContent = ticketData.description;

        // Update priority badge
        const priorityElement = document.getElementById('ticketPriority');
        priorityElement.className = `status-badge status-${this.getPriorityClass(ticketData.priority)}`;
        priorityElement.textContent = ticketData.priority;

        // Update status badge
        const statusElement = document.getElementById('ticketStatus');
        statusElement.className = `status-badge status-${this.getStatusClass(ticketData.status)}`;
        statusElement.textContent = ticketData.status;

        // Update assigned to
        document.getElementById('ticketAssigned').textContent = ticketData.assignedTo || 'Unassigned';

        window.modalSystem.openModal('viewTicketModal');
    }

    createTicket() {
        const form = document.getElementById('newTicketForm');
        const formData = new FormData(form);
        const ticketData = Object.fromEntries(formData.entries());

        // Validate required fields
        if (!ticketData.customerName || !ticketData.customerEmail || !ticketData.issueType || !ticketData.priority || !ticketData.description) {
            window.notificationSystem.error('Validation Error', 'Please fill in all required fields');
            return;
        }

        // Simulate API call
        setTimeout(() => {
            window.modalSystem.closeModal('newTicketModal');
            window.notificationSystem.success('Ticket Created', 'Support ticket has been created successfully');
            
            // Reset form
            form.reset();
            
            // In a real app, refresh the tickets table here
            console.log('New ticket created:', ticketData);
        }, 500);
    }

    resolveTicket(ticketId) {
        this.updateTicketStatus(ticketId, 'resolved');
        window.modalSystem.closeModal('viewTicketModal');
        window.notificationSystem.success('Ticket Resolved', `Ticket #TKT-${ticketId.padStart(5, '0')} has been marked as resolved`);
    }

    escalateTicket(ticketId) {
        // Update priority to high/critical
        const row = document.querySelector(`[data-id="${ticketId}"]`).closest('tr');
        const priorityCell = row.querySelector('[data-label="Priority"] .status-badge');
        
        if (priorityCell) {
            priorityCell.className = 'status-badge status-urgent';
            priorityCell.textContent = 'Critical';
        }

        window.modalSystem.closeModal('viewTicketModal');
        window.notificationSystem.warning('Ticket Escalated', `Ticket #TKT-${ticketId.padStart(5, '0')} has been escalated to critical priority`);
    }

    openAssignModal(ticketId = null) {
        const id = ticketId || this.currentTicket;
        this.currentTicket = id;
        
        window.modalSystem.closeModal('viewTicketModal');
        window.modalSystem.openModal('assignTicketModal');
    }

    assignTicket() {
        const form = document.getElementById('assignTicketForm');
        const formData = new FormData(form);
        const assignData = Object.fromEntries(formData.entries());

        if (!assignData.assignTo) {
            window.notificationSystem.error('Validation Error', 'Please select a team member to assign');
            return;
        }

        // Update UI
        const row = document.querySelector(`[data-id="${this.currentTicket}"]`).closest('tr');
        const assignedCell = row.querySelector('[data-label="Assigned To"]');
        
        if (assignedCell) {
            const assigneeName = form.querySelector(`option[value="${assignData.assignTo}"]`).textContent.split(' - ')[0];
            assignedCell.textContent = assigneeName;
        }

        window.modalSystem.closeModal('assignTicketModal');
        window.notificationSystem.success('Ticket Assigned', `Ticket has been assigned successfully`);
        
        // Reset form
        form.reset();
    }

    updateTicketStatus(ticketId, status) {
        const row = document.querySelector(`[data-id="${ticketId}"]`).closest('tr');
        const statusCell = row.querySelector('[data-label="Status"] .status-badge');
        
        if (statusCell) {
            statusCell.className = `status-badge status-${this.getStatusClass(status)}`;
            statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        }

        // Update last update time
        const updateCell = row.querySelector('[data-label="Last Update"]');
        if (updateCell) {
            updateCell.textContent = 'Just now';
        }
    }

    deleteTicket(ticketId) {
        window.modalSystem.showConfirmation(
            'Delete Ticket',
            `Are you sure you want to delete ticket #TKT-${ticketId.padStart(5, '0')}?`,
            () => {
                // Remove from UI
                const row = document.querySelector(`[data-id="${ticketId}"]`).closest('tr');
                if (row) {
                    row.remove();
                }
                
                window.notificationSystem.success('Ticket Deleted', 'Ticket has been deleted successfully');
                console.log(`Ticket ${ticketId} deleted`);
            }
        );
    }

    filterTickets(filterType, value) {
        const rows = document.querySelectorAll('.data-table tbody tr');
        
        rows.forEach(row => {
            if (!value) {
                row.style.display = '';
                return;
            }

            let cellSelector;
            if (filterType === 'priority') {
                cellSelector = '[data-label="Priority"] .status-badge';
            } else if (filterType === 'status') {
                cellSelector = '[data-label="Status"] .status-badge';
            }

            const cell = row.querySelector(cellSelector);
            const cellValue = cell?.textContent.toLowerCase();
            
            row.style.display = cellValue === value ? '' : 'none';
        });
    }

    getPriorityClass(priority) {
        const priorityMap = {
            'low': 'completed',
            'medium': 'pending',
            'high': 'urgent',
            'critical': 'urgent'
        };
        return priorityMap[priority.toLowerCase()] || 'pending';
    }

    getStatusClass(status) {
        const statusMap = {
            'open': 'pending',
            'in progress': 'warning',
            'resolved': 'completed',
            'closed': 'completed'
        };
        return statusMap[status.toLowerCase()] || 'pending';
    }

    initializeFilters() {
        // Initialize any default filters or sorting
    }

    getTicketData(ticketId) {
        // Mock data - in real app, fetch from API
        const mockData = {
            '125': {
                customer: 'James Wilson',
                email: '<EMAIL>',
                type: 'Hosting Issue',
                priority: 'High',
                status: 'Open',
                assignedTo: 'John Doe',
                created: 'Today, 10:15',
                updated: '2 hours ago',
                description: 'Our website has been experiencing intermittent downtime for the past 24 hours. Users are reporting 503 errors when trying to access the site. This is affecting our business operations significantly. Please investigate and resolve as soon as possible.'
            },
            '124': {
                customer: 'Lisa Anderson',
                email: '<EMAIL>',
                type: 'GPS Device',
                priority: 'Medium',
                status: 'In Progress',
                assignedTo: 'Jane Smith',
                created: 'Today, 08:45',
                updated: '1 hour ago',
                description: 'GPS tracker device is not updating location properly. The last known location was 6 hours ago. Device appears to be online but location data is not being transmitted.'
            }
        };

        return mockData[ticketId] || mockData['125'];
    }
}

// Initialize support manager
document.addEventListener('DOMContentLoaded', () => {
    window.supportManager = new SupportManager();
});
