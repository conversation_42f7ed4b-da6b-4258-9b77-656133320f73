:root {
            /* Theme colors - Matching DigitalAce */
            --primary: #7E57C2;
            --primary-dark: #5E35B1;
            --secondary: #26A69A;
            --accent: #FFCA28;
            --dark: #263238;
            --light: #f5f7fa;
            --gray: #78909C;
            --light-gray: #ECEFF1;
            --card-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
            --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
            --nav-height: 70px;
            --sidebar-width: 260px;
            
            /* Theme-specific variables */
            --bg-gradient: linear-gradient(135deg, #2c1b47 0%, #1c2331 100%);
            --header-bg: rgba(38, 50, 56, 0.95);
            --card-bg: rgba(38, 50, 56, 0.8);
            --text-color: #f5f7fa;
            --text-secondary: #CFD8DC;
            --border-color: rgba(126, 87, 194, 0.3);
            --footer-bg: rgba(38, 50, 56, 0.95);
        }

        /* Light Theme Overrides */
        [data-theme="light"] {
            --bg-gradient: linear-gradient(135deg, #f0f2f5 0%, #e4e6e9 100%);
            --header-bg: rgba(255, 255, 255, 0.95);
            --card-bg: rgba(255, 255, 255, 0.9);
            --text-color: #263238;
            --text-secondary: #546E7A;
            --border-color: rgba(206, 212, 218, 0.5);
            --footer-bg: rgba(255, 255, 255, 0.95);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', 'Segoe UI', sans-serif;
        }

        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            color: var(--text-color);
            display: flex;
            transition: var(--transition);
            overflow-x: hidden;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--header-bg);
            backdrop-filter: blur(10px);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            border-right: 1px solid var(--border-color);
            transition: var(--transition);
            overflow-y: auto;
            padding-bottom: 30px;
        }

        .sidebar-header {
            padding: 20px 25px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-color);
            text-decoration: none;
        }

        .logo i {
            color: var(--accent);
        }

        .sidebar-menu {
            padding: 25px 0;
        }

        .menu-title {
            padding: 10px 25px;
            font-size: 0.85rem;
            text-transform: uppercase;
            color: var(--text-secondary);
            letter-spacing: 1px;
            margin-top: 15px;
        }

        .menu-item {
            padding: 12px 25px;
            display: flex;
            align-items: center;
            gap: 15px;
            color: var(--text-color);
            text-decoration: none;
            transition: var(--transition);
            font-weight: 500;
            position: relative;
            margin: 5px 0;
        }

        .menu-item:hover, .menu-item.active {
            background: rgba(126, 87, 194, 0.15);
            color: var(--accent);
        }

        .menu-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: var(--accent);
        }

        .menu-item i {
            width: 24px;
            text-align: center;
        }

        /* Main Content Area */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: var(--transition);
            padding-top: var(--nav-height);
            min-height: 100vh;
        }

        /* Top Navigation */
        .top-nav {
            position: fixed;
            top: 0;
            right: 0;
            height: var(--nav-height);
            background: var(--header-bg);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
            z-index: 900;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
            left: var(--sidebar-width);
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .menu-toggle {
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            display: none;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .theme-toggle {
            background: transparent;
            border: none;
            color: var(--text-color);
            font-size: 1.3rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .theme-toggle:hover {
            color: var(--accent);
            transform: rotate(20deg);
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
        }

        .user-info {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            font-size: 0.95rem;
        }

        .user-role {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .user-dropdown {
            position: absolute;
            top: 55px;
            right: 0;
            background: var(--card-bg);
            border-radius: 10px;
            padding: 15px;
            width: 220px;
            box-shadow: var(--card-shadow);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            display: none;
            z-index: 1000;
        }

        .user-dropdown.active {
            display: block;
        }

        .dropdown-item {
            padding: 10px 15px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
            color: var(--text-color);
            text-decoration: none;
            transition: var(--transition);
        }

        .dropdown-item:hover {
            background: rgba(126, 87, 194, 0.15);
            color: var(--accent);
        }

        .dropdown-divider {
            height: 1px;
            background: var(--border-color);
            margin: 10px 0;
        }

        /* Content Container */
        .content-container {
            padding: 30px;
        }

        /* Dashboard Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 25px;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--card-shadow);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-title {
            color: var(--text-secondary);
            font-size: 0.95rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: rgba(126, 87, 194, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--accent);
            font-size: 1.5rem;
        }

        .stat-value {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--secondary);
            font-weight: 500;
        }

        .stat-trend.down {
            color: #ef5350;
        }

        /* Data Tables */
        .data-section {
            margin-bottom: 40px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .section-actions {
            display: flex;
            gap: 15px;
        }

        .action-btn {
            padding: 10px 20px;
            border-radius: 30px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
            border: none;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            border: 2px solid var(--primary);
            box-shadow: 0 5px 15px rgba(126, 87, 194, 0.3);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary);
            color: var(--primary);
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(126, 87, 194, 0.4);
        }

        .data-table {
            width: 100%;
            background: var(--card-bg);
            border-radius: 15px;
            border-collapse: collapse;
            overflow: hidden;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }

        .data-table th {
            background: rgba(126, 87, 194, 0.15);
            padding: 18px 20px;
            text-align: left;
            font-weight: 600;
            color: var(--accent);
        }

        .data-table td {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-color);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover {
            background: rgba(126, 87, 194, 0.08);
        }

        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 30px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending {
            background: rgba(255, 202, 40, 0.15);
            color: var(--accent);
        }

        .status-completed {
            background: rgba(38, 166, 154, 0.15);
            color: var(--secondary);
        }

        .status-urgent {
            background: rgba(239, 83, 80, 0.15);
            color: #ef5350;
        }

        .action-btns {
            display: flex;
            gap: 10px;
        }

        .table-btn {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            background: rgba(126, 87, 194, 0.1);
            color: var(--primary);
            border: none;
        }

        .table-btn:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-3px);
        }

        .table-btn.delete {
            background: rgba(239, 83, 80, 0.1);
            color: #ef5350;
        }

        .table-btn.delete:hover {
            background: #ef5350;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 1100px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .top-nav {
                left: 0;
            }
            
            .menu-toggle {
                display: block;
            }
        }

        @media (max-width: 768px) {
            .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .section-actions {
                width: 100%;
                flex-wrap: wrap;
            }
            
            .user-info {
                display: none;
            }
            
            .content-container {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .action-btn {
                padding: 8px 15px;
            }
        }

        /* Overlay for mobile sidebar */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 999;
            display: none;
        }
        
        .sidebar-overlay.active {
            display: block;
        }