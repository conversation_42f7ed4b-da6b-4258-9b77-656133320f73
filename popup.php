<?php
// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "your_database_name";

// Initialize variables
$success_message = "";
$error_message = "";
$form_submitted = false;

// Create connection using mysqli_connect
$conn = mysqli_connect($servername, $username, $password, $dbname);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $form_submitted = true;

    // Get form data and sanitize
    $fullName = mysqli_real_escape_string($conn, $_POST['fullName']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $phone = mysqli_real_escape_string($conn, $_POST['phone']);
    $subject = mysqli_real_escape_string($conn, $_POST['subject']);
    $message = mysqli_real_escape_string($conn, $_POST['message']);
    $newsletter = isset($_POST['newsletter']) ? 1 : 0;

    // Validate required fields
    if (empty($fullName) || empty($email) || empty($subject) || empty($message)) {
        $error_message = "Please fill in all required fields.";
    } else {
        // Insert data into products table
        $sql = "INSERT INTO products (full_name, email, phone, subject, message, newsletter, created_at)
                VALUES ('$fullName', '$email', '$phone', '$subject', '$message', '$newsletter', NOW())";

        if (mysqli_query($conn, $sql)) {
            $success_message = "Form submitted successfully! Thank you for your message.";
        } else {
            $error_message = "Error: " . $sql . "<br>" . mysqli_error($conn);
        }
    }
}

// Close connection
mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form - DigitalAce</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./assets/css/theme-constants.css">
    <link rel="stylesheet" href="./assets/css/index.css">
    <link rel="stylesheet" href="./assets/css/modal-system.css">
    <link rel="stylesheet" href="./assets/css/notification-system.css">
    <link rel="stylesheet" href="./assets/css/responsive-enhancements.css">
    <style>
        body {
            background: var(--bg-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: var(--font-family-primary);
            margin: 0;
            padding: var(--space-4);
        }

        .demo-container {
            text-align: center;
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-xl);
            padding: var(--space-16);
            box-shadow: var(--card-shadow-hover);
            max-width: 600px;
            width: 100%;
        }

        .demo-title {
            font-size: var(--font-3xl);
            font-weight: var(--font-bold);
            color: var(--text-color);
            margin-bottom: var(--space-4);
            text-shadow: var(--text-shadow);
        }

        .demo-description {
            color: var(--text-secondary);
            margin-bottom: var(--space-8);
            line-height: var(--leading-relaxed);
        }

        .theme-toggle {
            position: absolute;
            top: var(--space-4);
            right: var(--space-4);
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: var(--font-lg);
        }

        .theme-toggle:hover {
            background: var(--hover-bg);
            transform: rotate(180deg);
        }

        .success-message, .error-message {
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-6);
            font-weight: var(--font-semibold);
        }

        .success-message {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .error-message {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .form-container {
            background: var(--card-bg);
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            margin-top: var(--space-6);
            border: 1px solid var(--border-color);
        }

        .form-title {
            font-size: var(--font-2xl);
            font-weight: var(--font-bold);
            color: var(--text-color);
            margin-bottom: var(--space-6);
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <button class="theme-toggle" id="themeToggle">
        <i class="fas fa-moon"></i>
    </button>

    <!-- Main Demo Container -->
    <div class="demo-container">
        <h1 class="demo-title">
            <i class="fas fa-envelope"></i>
            Contact Form
        </h1>
        <p class="demo-description">
            Fill out the form below to send us a message. We'll get back to you as soon as possible.
        </p>

        <!-- Display success or error messages -->
        <?php if (!empty($success_message)): ?>
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Contact Form -->
        <div class="form-container">
            <h3 class="form-title">
                <i class="fas fa-edit"></i>
                Send us a Message
            </h3>

            <form class="modal-form" id="contactForm" method="post" action="popup.php">
                <div class="form-group">
                    <label class="form-label">Full Name</label>
                    <input name="fullName" type="text" class="form-input" placeholder="Enter your full name"
                           value="<?php echo $form_submitted ? htmlspecialchars($_POST['fullName'] ?? '') : ''; ?>" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Email Address</label>
                    <input name="email" type="email" class="form-input" placeholder="Enter your email"
                           value="<?php echo $form_submitted ? htmlspecialchars($_POST['email'] ?? '') : ''; ?>" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Phone Number</label>
                    <input name="phone" type="tel" class="form-input" placeholder="Enter your phone number"
                           value="<?php echo $form_submitted ? htmlspecialchars($_POST['phone'] ?? '') : ''; ?>">
                </div>
                <div class="form-group">
                    <label class="form-label">Subject</label>
                    <select class="form-input form-select" name="subject" required>
                        <option value="">Select a subject</option>
                        <option value="general" <?php echo ($form_submitted && ($_POST['subject'] ?? '') === 'general') ? 'selected' : ''; ?>>General Inquiry</option>
                        <option value="support" <?php echo ($form_submitted && ($_POST['subject'] ?? '') === 'support') ? 'selected' : ''; ?>>Technical Support</option>
                        <option value="billing" <?php echo ($form_submitted && ($_POST['subject'] ?? '') === 'billing') ? 'selected' : ''; ?>>Billing Question</option>
                        <option value="feedback" <?php echo ($form_submitted && ($_POST['subject'] ?? '') === 'feedback') ? 'selected' : ''; ?>>Feedback</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Message</label>
                    <textarea name="message" class="form-input form-textarea" placeholder="Enter your message here..."
                              required style="min-height: 100px;"><?php echo $form_submitted ? htmlspecialchars($_POST['message'] ?? '') : ''; ?></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" style="display: flex; align-items: center; gap: 8px;">
                        <input type="checkbox" name="newsletter" style="margin: 0;"
                               <?php echo ($form_submitted && isset($_POST['newsletter'])) ? 'checked' : ''; ?>>
                        Subscribe to our newsletter
                    </label>
                </div>
                <button name="submit" type="submit" class="modal-btn modal-btn-primary">
                    <i class="fas fa-paper-plane"></i>
                    Submit Message
                </button>
            </form>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Include JavaScript Files -->
    <script src="./assets/js/notification-system.js"></script>

    <script>
        // Initialize the form functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Theme toggle functionality
            const themeToggle = document.getElementById('themeToggle');
            const currentTheme = localStorage.getItem('theme') || 'dark';

            // Set initial theme
            document.documentElement.setAttribute('data-theme', currentTheme);
            updateThemeIcon(currentTheme);

            themeToggle.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
            });

            function updateThemeIcon(theme) {
                const icon = themeToggle.querySelector('i');
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            // Initialize notification system
            window.notificationSystem = new NotificationSystem();

            <?php if (!empty($success_message)): ?>
            // Show success notification
            setTimeout(() => {
                window.notificationSystem.success(
                    'Message Sent!',
                    'Thank you for your message. We will get back to you soon.',
                    { duration: 5000 }
                );
            }, 500);
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
            // Show error notification
            setTimeout(() => {
                window.notificationSystem.error(
                    'Submission Failed',
                    'Please check the form and try again.',
                    { duration: 5000 }
                );
            }, 500);
            <?php endif; ?>
        });
    </script>
</body>
</html>
