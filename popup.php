<?php
// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "your_database_name";

// Create connection using mysqli_connect
$conn = mysqli_connect($servername, $username, $password, $dbname);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data and sanitize
    $fullName = mysqli_real_escape_string($conn, $_POST['fullName']);
    $email = mysqli_real_escape_string($conn, $_POST['email']);
    $phone = mysqli_real_escape_string($conn, $_POST['phone']);
    $subject = mysqli_real_escape_string($conn, $_POST['subject']);
    $message = mysqli_real_escape_string($conn, $_POST['message']);
    $newsletter = isset($_POST['newsletter']) ? 1 : 0;
    
    // Validate required fields
    if (empty($fullName) || empty($email) || empty($subject) || empty($message)) {
        $error_message = "Please fill in all required fields.";
    } else {
        // Insert data into products table
        $sql = "INSERT INTO products (full_name, email, phone, subject, message, newsletter, created_at) 
                VALUES ('$fullName', '$email', '$phone', '$subject', '$message', '$newsletter', NOW())";
        
        if (mysqli_query($conn, $sql)) {
            $success_message = "Form submitted successfully! Thank you for your message.";
        } else {
            $error_message = "Error: " . $sql . "<br>" . mysqli_error($conn);
        }
    }
}

// Close connection
mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Submission Result</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .result-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .success-icon {
            color: #28a745;
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .error-icon {
            color: #dc3545;
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .result-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .result-message {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .back-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .form-data {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .form-data h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .data-item {
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .data-label {
            font-weight: bold;
            color: #495057;
            display: inline-block;
            width: 120px;
        }

        .data-value {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="result-container">
        <?php if (isset($success_message)): ?>
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1 class="result-title">Success!</h1>
            <p class="result-message"><?php echo $success_message; ?></p>
            
            <div class="form-data">
                <h3><i class="fas fa-info-circle"></i> Submitted Information</h3>
                <div class="data-item">
                    <span class="data-label">Name:</span>
                    <span class="data-value"><?php echo htmlspecialchars($_POST['fullName']); ?></span>
                </div>
                <div class="data-item">
                    <span class="data-label">Email:</span>
                    <span class="data-value"><?php echo htmlspecialchars($_POST['email']); ?></span>
                </div>
                <div class="data-item">
                    <span class="data-label">Phone:</span>
                    <span class="data-value"><?php echo htmlspecialchars($_POST['phone']); ?></span>
                </div>
                <div class="data-item">
                    <span class="data-label">Subject:</span>
                    <span class="data-value"><?php echo htmlspecialchars($_POST['subject']); ?></span>
                </div>
                <div class="data-item">
                    <span class="data-label">Message:</span>
                    <span class="data-value"><?php echo htmlspecialchars($_POST['message']); ?></span>
                </div>
                <div class="data-item">
                    <span class="data-label">Newsletter:</span>
                    <span class="data-value"><?php echo isset($_POST['newsletter']) ? 'Yes' : 'No'; ?></span>
                </div>
            </div>
            
        <?php elseif (isset($error_message)): ?>
            <div class="error-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <h1 class="result-title">Error!</h1>
            <p class="result-message"><?php echo $error_message; ?></p>
            
        <?php else: ?>
            <div class="error-icon">
                <i class="fas fa-question-circle"></i>
            </div>
            <h1 class="result-title">No Data Received</h1>
            <p class="result-message">No form data was submitted. Please go back and fill out the form.</p>
        <?php endif; ?>
        
        <a href="popup.html" class="back-button">
            <i class="fas fa-arrow-left"></i>
            Back to Form
        </a>
    </div>
</body>
</html>
